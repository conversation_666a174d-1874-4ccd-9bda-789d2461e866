@extends('layouts.app')

@section('title', 'Subscription Expired')

@section('content')
<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <div class="text-center">
            <div class="mx-auto h-12 w-12 text-red-600">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                Subscription Expired
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                Your subscription has expired. Please renew to continue using our services.
            </p>
        </div>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-2xl">
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <!-- Current Status -->
            <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">
                            Access Restricted
                        </h3>
                        <div class="mt-2 text-sm text-red-700">
                            <p>Your subscription status: <strong>{{ ucfirst($currentTenant->subscription_status) }}</strong></p>
                            @if($currentTenant->trial_ends_at)
                                <p>Trial ended: {{ $currentTenant->trial_ends_at->format('M d, Y') }}</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- What's Affected -->
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-3">What's affected:</h3>
                <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-center">
                        <svg class="h-4 w-4 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                        Creating new campaigns and messages
                    </li>
                    <li class="flex items-center">
                        <svg class="h-4 w-4 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                        Adding new contacts
                    </li>
                    <li class="flex items-center">
                        <svg class="h-4 w-4 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                        Bot automation features
                    </li>
                    <li class="flex items-center">
                        <svg class="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        View existing data (read-only access)
                    </li>
                </ul>
            </div>

            <!-- Renewal Options -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900">Choose a plan to continue:</h3>
                
                @foreach($plans as $plan)
                <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-500 transition duration-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-semibold text-gray-900">{{ $plan->name }}</h4>
                            <p class="text-sm text-gray-600">{{ $plan->description }}</p>
                            <div class="mt-1">
                                <span class="text-lg font-bold text-gray-900">{{ $plan->formatted_price }}</span>
                                @if($plan->price > 0)
                                    <span class="text-gray-500">/{{ $plan->billing_cycle }}</span>
                                @endif
                            </div>
                        </div>
                        <div>
                            <a href="{{ route('subscription.upgrade', ['plan' => $plan->id]) }}" 
                               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition duration-200">
                                @if($plan->price == 0) Start Free @else Renew @endif
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Contact Support -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="text-center">
                    <p class="text-sm text-gray-600">
                        Need help choosing a plan or have questions?
                    </p>
                    <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-700 font-medium">
                        Contact our support team
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

