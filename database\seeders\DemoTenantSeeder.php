<?php

namespace Database\Seeders;

use App\Models\Tenant;
use App\Models\User;
use App\Models\SubscriptionPlan;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class DemoTenantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = SubscriptionPlan::all();
        
        if ($plans->isEmpty()) {
            $this->command->error('No subscription plans found. Please run SubscriptionPlanSeeder first.');
            return;
        }

        $demoTenants = [
            [
                'name' => 'Demo Company',
                'slug' => 'demo',
                'domain' => null,
                'status' => 'active',
                'subscription_status' => 'active',
                'plan_slug' => 'plan-m2-growth',
                'owner' => [
                    'firstname' => 'Demo',
                    'lastname' => 'User',
                    'email' => '<EMAIL>',
                    'password' => '12345678'
                ]
            ],
            [
                'name' => 'Startup Inc',
                'slug' => 'startup',
                'domain' => null,
                'status' => 'active',
                'subscription_status' => 'trialing',
                'plan_slug' => 'plan-m1-starter',
                'trial_ends_at' => now()->addDays(10),
                'owner' => [
                    'firstname' => 'John',
                    'lastname' => 'Startup',
                    'email' => '<EMAIL>',
                    'password' => '12345678'
                ]
            ],
            [
                'name' => 'Enterprise Corp',
                'slug' => 'enterprise',
                'domain' => null,
                'status' => 'active',
                'subscription_status' => 'active',
                'plan_slug' => 'plan-m2-growth',
                'owner' => [
                    'firstname' => 'Jane',
                    'lastname' => 'Enterprise',
                    'email' => '<EMAIL>',
                    'password' => '12345678'
                ]
            ]
        ];

        foreach ($demoTenants as $tenantData) {
            $plan = $plans->where('slug', $tenantData['plan_slug'])->first();
            
            if (!$plan) {
                $this->command->warn("Plan {$tenantData['plan_slug']} not found, skipping tenant {$tenantData['name']}");
                continue;
            }

            DB::transaction(function () use ($tenantData, $plan) {
                // Create tenant
                $tenant = Tenant::updateOrCreate(
                    ['slug' => $tenantData['slug']],
                    [
                        'name' => $tenantData['name'],
                        'domain' => $tenantData['domain'],
                        'status' => $tenantData['status'],
                        'subscription_plan_id' => $plan->id,
                        'subscription_status' => $tenantData['subscription_status'],
                        'trial_ends_at' => $tenantData['trial_ends_at'] ?? null,
                    ]
                );

                // Create owner user
                $owner = User::updateOrCreate(
                    ['email' => $tenantData['owner']['email']],
                    [
                        'firstname' => $tenantData['owner']['firstname'],
                        'lastname' => $tenantData['owner']['lastname'],
                        'password' => Hash::make($tenantData['owner']['password']),
                        'is_admin' => true,
                        'active' => true,
                        'email_verified_at' => now(),
                    ]
                );

                // Associate user with tenant
                $tenant->users()->syncWithoutDetaching([
                    $owner->id => ['role' => 'owner']
                ]);

                $this->command->info("Created demo tenant: {$tenant->name} with owner: {$owner->email}");
            });
        }

        $this->command->info('Demo tenants created successfully!');
    }
}

