        <?php if (isset($component)) { $__componentOriginal4775cfa36403047d012e8c3e57d8f2a7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4775cfa36403047d012e8c3e57d8f2a7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.account-disconnected','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('account-disconnected'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4775cfa36403047d012e8c3e57d8f2a7)): ?>
<?php $attributes = $__attributesOriginal4775cfa36403047d012e8c3e57d8f2a7; ?>
<?php unset($__attributesOriginal4775cfa36403047d012e8c3e57d8f2a7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4775cfa36403047d012e8c3e57d8f2a7)): ?>
<?php $component = $__componentOriginal4775cfa36403047d012e8c3e57d8f2a7; ?>
<?php unset($__componentOriginal4775cfa36403047d012e8c3e57d8f2a7); ?>
<?php endif; ?><?php /**PATH C:\laragon\www\mywhats\storage\framework\views/2e5c0c58b4917be3be27c9fef68bbf0b.blade.php ENDPATH**/ ?>