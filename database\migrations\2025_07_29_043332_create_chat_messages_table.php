<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_messages', function (Blueprint $table) {
            $table->id();
            $table->string('message_id')->nullable();
            $table->string('chat_id')->nullable();
            $table->string('from_phone_number')->nullable();
            $table->string('to_phone_number')->nullable();
            $table->text('message')->nullable();
            $table->string('message_type')->default('text');
            $table->string('direction')->default('inbound'); // inbound/outbound
            $table->string('status')->default('received'); // sent, delivered, read, failed
            $table->json('metadata')->nullable();
            $table->timestamp('message_timestamp')->nullable();
            $table->boolean('is_read')->default(false);
            $table->timestamps();
            
            $table->index(['chat_id', 'message_timestamp']);
            $table->index(['from_phone_number']);
            $table->index(['to_phone_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_messages');
    }
};
