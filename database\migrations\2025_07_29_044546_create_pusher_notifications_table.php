<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pusher_notifications', function (Blueprint $table) {
            $table->id();
            $table->boolean('isread')->default(0);
            $table->boolean('isread_inline')->default(0);
            $table->string('date');
            $table->text('description');
            $table->unsignedBigInteger('fromuserid');
            $table->unsignedBigInteger('fromclientid')->default(0);
            $table->string('from_fullname');
            $table->unsignedBigInteger('touserid');
            $table->unsignedBigInteger('fromcompany')->nullable();
            $table->string('link')->nullable();
            $table->text('additional_data')->nullable();
            $table->timestamps();
            
            $table->index(['touserid']);
            $table->index(['fromuserid']);
            $table->index(['isread']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pusher_notifications');
    }
};
