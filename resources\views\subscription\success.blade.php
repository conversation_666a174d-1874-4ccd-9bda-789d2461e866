@extends('layouts.app')

@section('title', 'Subscription Success')

@section('content')
<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <div class="text-center">
            <div class="mx-auto h-12 w-12 text-green-600">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                Subscription Successful!
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                Welcome to your new plan. You now have access to all features.
            </p>
        </div>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-lg">
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <!-- Success Message -->
            <div class="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-green-800">
                            Payment Processed Successfully
                        </h3>
                        <div class="mt-2 text-sm text-green-700">
                            <p>Your subscription has been activated and you can now access all features of your plan.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Plan Details -->
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-3">Your New Plan</h3>
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex justify-between items-center mb-2">
                        <span class="font-semibold text-gray-900">{{ $currentPlan->name }}</span>
                        <span class="text-lg font-bold text-blue-600">{{ $currentPlan->formatted_price }}</span>
                    </div>
                    <p class="text-sm text-gray-600 mb-3">{{ $currentPlan->description }}</p>
                    
                    <!-- Key Features -->
                    <div class="space-y-2">
                        <h4 class="text-sm font-medium text-gray-700">What you get:</h4>
                        <div class="grid grid-cols-2 gap-2 text-sm">
                            @foreach($currentPlan->features as $feature => $value)
                            <div class="flex items-center">
                                <svg class="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-700">
                                    {{ ucfirst(str_replace('_', ' ', $feature)) }}: 
                                    @if(is_bool($value))
                                        {{ $value ? 'Yes' : 'No' }}
                                    @elseif($value == -1)
                                        Unlimited
                                    @else
                                        {{ $value }}
                                    @endif
                                </span>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-3">What's Next?</h3>
                <div class="space-y-3">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 text-blue-600 text-sm font-medium">1</div>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-gray-700">Start creating your WhatsApp campaigns and templates</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 text-blue-600 text-sm font-medium">2</div>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-gray-700">Import your contacts and organize them</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 text-blue-600 text-sm font-medium">3</div>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-gray-700">Set up automation bots to engage with customers</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-3">
                <a href="{{ route('dashboard') }}" 
                   class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200">
                    Go to Dashboard
                </a>
                
                <a href="{{ route('subscription.plans') }}" 
                   class="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200">
                    View All Plans
                </a>
            </div>

            <!-- Support -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="text-center">
                    <p class="text-sm text-gray-600">
                        Need help getting started?
                    </p>
                    <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-700 font-medium">
                        Contact our support team
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

