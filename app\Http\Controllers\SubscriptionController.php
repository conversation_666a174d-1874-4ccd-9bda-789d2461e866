<?php

namespace App\Http\Controllers;

use App\Models\SubscriptionPlan;
use App\Models\Tenant;
use App\Services\FeatureGateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SubscriptionController extends Controller
{
    protected FeatureGateService $featureGate;

    public function __construct(FeatureGateService $featureGate)
    {
        $this->featureGate = $featureGate;
    }

    /**
     * Display subscription plans
     */
    public function plans()
    {
        $plans = SubscriptionPlan::active()->orderByPrice()->get();
        $currentTenant = app('tenant');
        $currentPlan = $currentTenant->subscriptionPlan;
        $usage = $this->featureGate->getAllUsageLimits($currentTenant);

        return view('subscription.plans', compact('plans', 'currentPlan', 'usage', 'currentTenant'));
    }

    /**
     * Show subscription upgrade page
     */
    public function upgrade()
    {
        $currentTenant = app('tenant');
        $currentPlan = $currentTenant->subscriptionPlan;
        $plans = SubscriptionPlan::active()
                                ->where('price', '>', $currentPlan?->price ?? 0)
                                ->orderByPrice()
                                ->get();

        return view('subscription.upgrade', compact('plans', 'currentPlan', 'currentTenant'));
    }

    /**
     * Show subscription expired page
     */
    public function expired()
    {
        $currentTenant = app('tenant');
        $plans = SubscriptionPlan::active()->orderByPrice()->get();

        return view('subscription.expired', compact('plans', 'currentTenant'));
    }

    /**
     * Process subscription upgrade
     */
    public function processUpgrade(Request $request)
    {
        $request->validate([
            'plan_id' => 'required|exists:subscription_plans,id',
            'billing_cycle' => 'required|in:monthly,yearly'
        ]);

        $plan = SubscriptionPlan::findOrFail($request->plan_id);
        $currentTenant = app('tenant');

        // Calculate price based on billing cycle
        $price = $request->billing_cycle === 'yearly' ? $plan->yearly_price : $plan->monthly_price;

        // For demo purposes, we'll simulate payment success
        // In production, integrate with Stripe, Razorpay, etc.
        
        if ($this->processPayment($currentTenant, $plan, $price, $request->billing_cycle)) {
            // Update tenant subscription
            $currentTenant->update([
                'subscription_plan_id' => $plan->id,
                'subscription_status' => 'active',
                'trial_ends_at' => null
            ]);

            return redirect()->route('subscription.success')
                           ->with('success', 'Subscription upgraded successfully!');
        }

        return back()->with('error', 'Payment failed. Please try again.');
    }

    /**
     * Show subscription success page
     */
    public function success()
    {
        $currentTenant = app('tenant');
        $currentPlan = $currentTenant->subscriptionPlan;

        return view('subscription.success', compact('currentTenant', 'currentPlan'));
    }

    /**
     * Cancel subscription
     */
    public function cancel(Request $request)
    {
        $currentTenant = app('tenant');
        
        $currentTenant->update([
            'subscription_status' => 'cancelled'
        ]);

        return redirect()->route('subscription.plans')
                       ->with('success', 'Subscription cancelled successfully.');
    }

    /**
     * Show billing history
     */
    public function billing()
    {
        $currentTenant = app('tenant');
        // In production, fetch actual billing records from payment provider
        $billingHistory = collect(); // Placeholder

        return view('subscription.billing', compact('currentTenant', 'billingHistory'));
    }

    /**
     * Get usage statistics API
     */
    public function usage()
    {
        $currentTenant = app('tenant');
        $usage = $this->featureGate->getAllUsageLimits($currentTenant);
        $approaching = $this->featureGate->isApproachingLimits($currentTenant);

        return response()->json([
            'usage' => $usage,
            'approaching_limits' => $approaching,
            'subscription_status' => $currentTenant->subscription_status,
            'trial_ends_at' => $currentTenant->trial_ends_at,
            'plan' => $currentTenant->subscriptionPlan
        ]);
    }

    /**
     * Compare subscription plans
     */
    public function compare(Request $request)
    {
        $planIds = $request->input('plans', []);
        
        if (empty($planIds)) {
            $planIds = SubscriptionPlan::active()->pluck('id')->toArray();
        }

        $comparison = $this->featureGate->compareFeatures($planIds);
        
        return response()->json($comparison);
    }

    /**
     * Process payment (demo implementation)
     * In production, integrate with actual payment providers
     */
    private function processPayment(Tenant $tenant, SubscriptionPlan $plan, float $amount, string $cycle): bool
    {
        // Demo: Always return true for successful payment
        // In production, implement actual payment processing here
        
        // Log the payment attempt
        \Log::info('Payment processed', [
            'tenant_id' => $tenant->id,
            'plan_id' => $plan->id,
            'amount' => $amount,
            'billing_cycle' => $cycle
        ]);

        return true;
    }
}

