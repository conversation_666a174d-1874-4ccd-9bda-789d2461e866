@extends('layouts.app')

@section('title', 'Tenant Details - ' . $tenant->name)

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ $tenant->name }}</h1>
                <p class="text-gray-600 mt-2">Tenant details and management</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('superadmin.tenants.edit', $tenant) }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition duration-200">
                    Edit Tenant
                </a>
                <form method="POST" action="{{ route('superadmin.tenants.impersonate', $tenant) }}" class="inline">
                    @csrf
                    <button type="submit" 
                            class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition duration-200">
                        Login as Owner
                    </button>
                </form>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Info -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Name</label>
                            <p class="text-gray-900">{{ $tenant->name }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Slug</label>
                            <p class="text-gray-900">{{ $tenant->slug }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Domain</label>
                            <p class="text-gray-900">{{ $tenant->domain ?: 'Not set' }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Created</label>
                            <p class="text-gray-900">{{ $tenant->created_at->format('M d, Y H:i') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Subscription Info -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Subscription Details</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Current Plan</label>
                            <p class="text-gray-900">{{ $tenant->subscriptionPlan?->name ?? 'No Plan' }}</p>
                            @if($tenant->subscriptionPlan)
                                <p class="text-sm text-gray-500">{{ $tenant->subscriptionPlan->formatted_price }}/{{ $tenant->subscriptionPlan->billing_cycle }}</p>
                            @endif
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <div class="flex space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($tenant->status === 'active') bg-green-100 text-green-800
                                    @elseif($tenant->status === 'trial') bg-yellow-100 text-yellow-800
                                    @elseif($tenant->status === 'suspended') bg-red-100 text-red-800
                                    @else bg-gray-100 text-gray-800 @endif">
                                    {{ ucfirst($tenant->status) }}
                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($tenant->subscription_status === 'active') bg-green-100 text-green-800
                                    @elseif($tenant->subscription_status === 'trialing') bg-blue-100 text-blue-800
                                    @elseif($tenant->subscription_status === 'cancelled') bg-red-100 text-red-800
                                    @else bg-gray-100 text-gray-800 @endif">
                                    {{ ucfirst($tenant->subscription_status) }}
                                </span>
                            </div>
                        </div>
                        @if($tenant->trial_ends_at)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Trial Ends</label>
                            <p class="text-gray-900">{{ $tenant->trial_ends_at->format('M d, Y H:i') }}</p>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Usage Statistics -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Usage Statistics</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ $stats['total_contacts'] }}</div>
                            <div class="text-sm text-gray-600">Contacts</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">{{ $stats['total_campaigns'] }}</div>
                            <div class="text-sm text-gray-600">Campaigns</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">{{ $stats['total_messages'] }}</div>
                            <div class="text-sm text-gray-600">Messages</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-orange-600">{{ $stats['active_bots'] }}</div>
                            <div class="text-sm text-gray-600">Active Bots</div>
                        </div>
                    </div>
                </div>

                <!-- Users -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Users ({{ $tenant->users->count() }})</h2>
                    <div class="space-y-3">
                        @forelse($tenant->users as $user)
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <div class="font-medium text-gray-900">{{ $user->firstname }} {{ $user->lastname }}</div>
                                <div class="text-sm text-gray-500">{{ $user->email }}</div>
                            </div>
                            <div class="text-right">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($user->pivot->role === 'owner') bg-purple-100 text-purple-800
                                    @elseif($user->pivot->role === 'admin') bg-blue-100 text-blue-800
                                    @else bg-gray-100 text-gray-800 @endif">
                                    {{ ucfirst($user->pivot->role ?? 'User') }}
                                </span>
                                <div class="text-xs text-gray-500 mt-1">
                                    {{ $user->active ? 'Active' : 'Inactive' }}
                                </div>
                            </div>
                        </div>
                        @empty
                        <p class="text-gray-500 text-center py-4">No users found</p>
                        @endforelse
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        @if($tenant->status === 'active')
                            <form method="POST" action="{{ route('superadmin.tenants.suspend', $tenant) }}">
                                @csrf
                                <button type="submit" 
                                        class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition duration-200"
                                        onclick="return confirm('Are you sure you want to suspend this tenant?')">
                                    Suspend Tenant
                                </button>
                            </form>
                        @else
                            <form method="POST" action="{{ route('superadmin.tenants.activate', $tenant) }}">
                                @csrf
                                <button type="submit" 
                                        class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition duration-200">
                                    Activate Tenant
                                </button>
                            </form>
                        @endif
                        
                        <a href="{{ route('superadmin.tenants.edit', $tenant) }}" 
                           class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition duration-200 block text-center">
                            Edit Details
                        </a>
                        
                        <form method="POST" action="{{ route('superadmin.tenants.destroy', $tenant) }}">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition duration-200"
                                    onclick="return confirm('Are you sure you want to delete this tenant? This action cannot be undone.')">
                                Delete Tenant
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Usage Limits -->
                @if(!empty($usage))
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Usage Limits</h3>
                    <div class="space-y-3">
                        @foreach($usage as $resource => $data)
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="font-medium text-gray-700 capitalize">{{ str_replace('_', ' ', $resource) }}</span>
                                <span class="text-gray-500">
                                    @if($data['unlimited'])
                                        Unlimited
                                    @else
                                        {{ $data['current'] }}/{{ $data['limit'] }}
                                    @endif
                                </span>
                            </div>
                            @if(!$data['unlimited'])
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="h-2 rounded-full 
                                    @if($data['percentage'] >= 90) bg-red-500
                                    @elseif($data['percentage'] >= 75) bg-yellow-500
                                    @else bg-green-500 @endif" 
                                     style="width: {{ min(100, $data['percentage']) }}%"></div>
                            </div>
                            @endif
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

