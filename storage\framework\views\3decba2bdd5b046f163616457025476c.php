<?php $__env->startSection('title', 'Tenant Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-8">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Tenant Management</h1>
                <p class="text-gray-600 mt-2">Manage all tenants and their subscriptions</p>
            </div>
            <a href="<?php echo e(route('superadmin.tenants.create')); ?>" 
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition duration-200">
                Create New Tenant
            </a>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <form method="GET" action="<?php echo e(route('superadmin.tenants.index')); ?>" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text" name="search" value="<?php echo e(request('search')); ?>" 
                           placeholder="Name, domain, or slug"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select name="status" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Statuses</option>
                        <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                        <option value="trial" <?php echo e(request('status') === 'trial' ? 'selected' : ''); ?>>Trial</option>
                        <option value="suspended" <?php echo e(request('status') === 'suspended' ? 'selected' : ''); ?>>Suspended</option>
                        <option value="expired" <?php echo e(request('status') === 'expired' ? 'selected' : ''); ?>>Expired</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Subscription</label>
                    <select name="subscription_status" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Subscriptions</option>
                        <option value="active" <?php echo e(request('subscription_status') === 'active' ? 'selected' : ''); ?>>Active</option>
                        <option value="trialing" <?php echo e(request('subscription_status') === 'trialing' ? 'selected' : ''); ?>>Trialing</option>
                        <option value="cancelled" <?php echo e(request('subscription_status') === 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
                        <option value="past_due" <?php echo e(request('subscription_status') === 'past_due' ? 'selected' : ''); ?>>Past Due</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Plan</label>
                    <select name="plan" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Plans</option>
                        <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($plan->id); ?>" <?php echo e(request('plan') == $plan->id ? 'selected' : ''); ?>>
                            <?php echo e($plan->name); ?>

                        </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                
                <div class="flex items-end">
                    <button type="submit" class="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md font-medium transition duration-200">
                        Filter
                    </button>
                </div>
            </form>
        </div>

        <!-- Tenants Table -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tenant</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Users</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php $__empty_1 = true; $__currentLoopData = $tenants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($tenant->name); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($tenant->slug); ?></div>
                                    <?php if($tenant->domain): ?>
                                        <div class="text-xs text-blue-600"><?php echo e($tenant->domain); ?></div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php if($tenant->subscriptionPlan): ?>
                                    <div class="text-sm text-gray-900"><?php echo e($tenant->subscriptionPlan->name); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($tenant->subscriptionPlan->formatted_price); ?>/<?php echo e($tenant->subscriptionPlan->billing_cycle); ?></div>
                                <?php else: ?>
                                    <span class="text-sm text-gray-400">No Plan</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex flex-col space-y-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        <?php if($tenant->status === 'active'): ?> bg-green-100 text-green-800
                                        <?php elseif($tenant->status === 'trial'): ?> bg-yellow-100 text-yellow-800
                                        <?php elseif($tenant->status === 'suspended'): ?> bg-red-100 text-red-800
                                        <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                        <?php echo e(ucfirst($tenant->status)); ?>

                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        <?php if($tenant->subscription_status === 'active'): ?> bg-green-100 text-green-800
                                        <?php elseif($tenant->subscription_status === 'trialing'): ?> bg-blue-100 text-blue-800
                                        <?php elseif($tenant->subscription_status === 'cancelled'): ?> bg-red-100 text-red-800
                                        <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                        <?php echo e(ucfirst($tenant->subscription_status)); ?>

                                    </span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo e($tenant->users->count()); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo e($tenant->created_at->format('M d, Y')); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <a href="<?php echo e(route('superadmin.tenants.show', $tenant)); ?>" 
                                       class="text-blue-600 hover:text-blue-900">View</a>
                                    <a href="<?php echo e(route('superadmin.tenants.edit', $tenant)); ?>" 
                                       class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                    
                                    <?php if($tenant->status === 'active'): ?>
                                        <form method="POST" action="<?php echo e(route('superadmin.tenants.suspend', $tenant)); ?>" class="inline">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="text-red-600 hover:text-red-900"
                                                    onclick="return confirm('Are you sure you want to suspend this tenant?')">
                                                Suspend
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <form method="POST" action="<?php echo e(route('superadmin.tenants.activate', $tenant)); ?>" class="inline">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="text-green-600 hover:text-green-900">
                                                Activate
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                    
                                    <form method="POST" action="<?php echo e(route('superadmin.tenants.impersonate', $tenant)); ?>" class="inline">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="text-purple-600 hover:text-purple-900">
                                            Login
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                                No tenants found
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <?php if($tenants->hasPages()): ?>
            <div class="px-6 py-4 border-t border-gray-200">
                <?php echo e($tenants->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\mywhats\resources\views/superadmin/tenants/index.blade.php ENDPATH**/ ?>