<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class SuperAdminMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Check if user is a super admin
        if (!$this->isSuperAdmin($user)) {
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }
            
            abort(403, 'Access denied. Super admin privileges required.');
        }

        return $next($request);
    }

    /**
     * Check if user is a super admin
     */
    private function isSuperAdmin($user): bool
    {
        // Check if user has super admin role or is marked as super admin
        if (isset($user->is_super_admin) && $user->is_super_admin) {
            return true;
        }

        // Check if user has super admin permission
        if (method_exists($user, 'hasPermissionTo')) {
            return $user->hasPermissionTo('super-admin');
        }

        // Check if user email is in super admin list (for initial setup)
        $superAdminEmails = config('app.super_admin_emails', []);
        if (in_array($user->email, $superAdminEmails)) {
            return true;
        }

        return false;
    }
}

