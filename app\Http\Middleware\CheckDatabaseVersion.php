<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckDatabaseVersion
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip check for database upgrade routes
        if ($request->is('database-upgrade')) {
            return $this->databaseNeedsUpgrade()
                ? $next($request)
                : redirect()->route('admin.dashboard');
        }

        if ($request->is('upgrade')) {
            return $next($request);
        }

        // Skip check for SaaS routes that don't need database version check
        if ($request->is('superadmin/*') || $request->is('subscription/*')) {
            return $next($request);
        }

        if ($this->databaseNeedsUpgrade()) {
            return redirect()->route('database.upgrade');
        }

        return $next($request);
    }

    /**
     * Determine if the database needs an upgrade
     */
    protected function databaseNeedsUpgrade(): bool
    {
        try {
            // For SaaS platform, we'll use a simple version check
            $currentVersion = config('app.version', '1.0.0');

            // Get database version from settings
            $databaseVersion = get_setting('whats-mark.wm_version', '1.0.0');

            // Compare versions using semantic versioning
            return version_compare($currentVersion, $databaseVersion, '>');
        } catch (\Exception $e) {
            // Log error if logging function exists
            if (function_exists('whatsapp_log')) {
                whatsapp_log('Error checking database version', 'error', [
                    'error' => $e->getMessage(),
                ], $e);
            }

            // If we can't check, assume no upgrade is needed
            return false;
        }
    }
}
