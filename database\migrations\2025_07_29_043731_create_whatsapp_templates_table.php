<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('category')->default('MARKETING'); // MARKETING, UTILITY, AUTHENTICATION
            $table->string('language_code')->default('en');
            $table->string('language_policy')->default('deterministic'); // deterministic or fallback
            $table->json('components')->nullable(); // WhatsApp template components structure
            $table->text('header_text')->nullable();
            $table->text('body_text');
            $table->text('footer_text')->nullable();
            $table->json('buttons')->nullable(); // Call to action buttons
            $table->json('parameters')->nullable(); // Template variables/parameters
            $table->string('status')->default('PENDING'); // PENDING, APPROVED, REJECTED, DISABLED
            $table->string('quality_rating')->nullable(); // GREEN, YELLOW, RED
            $table->string('whatsapp_template_id')->nullable(); // WhatsApp Business API template ID
            $table->string('namespace')->nullable(); // WhatsApp namespace
            $table->boolean('is_active')->default(true);
            $table->integer('usage_count')->default(0);
            $table->timestamp('approved_at')->nullable();
            $table->timestamp('last_used_at')->nullable();
            $table->json('rejection_reason')->nullable();
            $table->json('metadata')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            
            $table->index(['category']);
            $table->index(['language_code']); 
            $table->index(['status']);
            $table->index(['is_active']);
            $table->index(['created_by']);
            $table->unique(['name', 'language_code']); // Template name must be unique per language
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_templates');
    }
};
