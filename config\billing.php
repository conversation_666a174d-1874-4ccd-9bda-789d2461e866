<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Payment Gateway
    |--------------------------------------------------------------------------
    |
    | This option controls the default payment gateway used for processing
    | subscription payments. Supported gateways: "stripe", "razorpay", "demo"
    |
    */
    'default_gateway' => env('BILLING_GATEWAY', 'demo'),

    /*
    |--------------------------------------------------------------------------
    | Payment Gateways Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for various payment gateways
    |
    */
    'gateways' => [
        'stripe' => [
            'public_key' => env('STRIPE_PUBLIC_KEY'),
            'secret_key' => env('STRIPE_SECRET_KEY'),
            'webhook_secret' => env('STRIPE_WEBHOOK_SECRET'),
            'currency' => env('STRIPE_CURRENCY', 'inr'),
        ],

        'razorpay' => [
            'key_id' => env('RAZORPAY_KEY_ID'),
            'key_secret' => env('RAZORPAY_KEY_SECRET'),
            'webhook_secret' => env('RAZORPAY_WEBHOOK_SECRET'),
            'currency' => env('RAZORPAY_CURRENCY', 'INR'),
        ],

        'demo' => [
            'enabled' => env('DEMO_PAYMENTS', true),
            'always_succeed' => env('DEMO_ALWAYS_SUCCEED', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Billing Settings
    |--------------------------------------------------------------------------
    |
    | General billing configuration options
    |
    */
    'settings' => [
        'currency' => env('BILLING_CURRENCY', 'INR'),
        'currency_symbol' => env('BILLING_CURRENCY_SYMBOL', '₹'),
        'trial_days' => env('TRIAL_DAYS', 14),
        'grace_period_days' => env('GRACE_PERIOD_DAYS', 3),
        'invoice_prefix' => env('INVOICE_PREFIX', 'WM'),
        'tax_rate' => env('TAX_RATE', 0.18), // 18% GST for India
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for payment gateway webhooks
    |
    */
    'webhooks' => [
        'stripe' => [
            'endpoint' => '/webhooks/stripe',
            'events' => [
                'invoice.payment_succeeded',
                'invoice.payment_failed',
                'customer.subscription.updated',
                'customer.subscription.deleted',
            ],
        ],

        'razorpay' => [
            'endpoint' => '/webhooks/razorpay',
            'events' => [
                'payment.captured',
                'payment.failed',
                'subscription.activated',
                'subscription.cancelled',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Limits
    |--------------------------------------------------------------------------
    |
    | Default feature limits for different plan types
    |
    */
    'default_limits' => [
        'free' => [
            'contacts' => 50,
            'campaigns' => 5,
            'templates' => 5,
            'bots' => 5,
            'staff' => 1,
            'monthly_messages' => 1000,
        ],

        'starter' => [
            'contacts' => 100,
            'campaigns' => 100,
            'templates' => 100,
            'bots' => 100,
            'staff' => 100,
            'monthly_messages' => 10000,
        ],

        'growth' => [
            'contacts' => 200,
            'campaigns' => 200,
            'templates' => 200,
            'bots' => 200,
            'staff' => 200,
            'monthly_messages' => 25000,
        ],

        'enterprise' => [
            'contacts' => -1, // unlimited
            'campaigns' => -1,
            'templates' => -1,
            'bots' => -1,
            'staff' => -1,
            'monthly_messages' => -1,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for billing-related notifications
    |
    */
    'notifications' => [
        'payment_success' => [
            'email' => true,
            'dashboard' => true,
        ],

        'payment_failed' => [
            'email' => true,
            'dashboard' => true,
            'retry_attempts' => 3,
        ],

        'trial_ending' => [
            'email' => true,
            'dashboard' => true,
            'days_before' => [7, 3, 1],
        ],

        'usage_limit_warning' => [
            'email' => true,
            'dashboard' => true,
            'threshold' => 80, // percentage
        ],
    ],
];

