<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop and recreate with correct structure
        Schema::dropIfExists('message_bots');
        
        Schema::create('message_bots', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('rel_type');
            $table->text('reply_text');
            $table->integer('reply_type');
            $table->json('trigger')->nullable();
            $table->text('bot_header')->nullable();
            $table->text('bot_footer')->nullable();
            $table->string('button1')->nullable();
            $table->string('button1_id')->nullable();
            $table->string('button2')->nullable();
            $table->string('button2_id')->nullable();
            $table->string('button3')->nullable();
            $table->string('button3_id')->nullable();
            $table->string('button_name')->nullable();
            $table->string('button_url')->nullable();
            $table->unsignedBigInteger('addedfrom');
            $table->boolean('is_bot_active')->default(1);
            $table->integer('sending_count')->default(0);
            $table->string('filename')->nullable();
            $table->timestamps();
            
            $table->index(['rel_type']);
            $table->index(['is_bot_active']);
            $table->index(['reply_type']);
            $table->index(['addedfrom']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('message_bots');
    }
};
