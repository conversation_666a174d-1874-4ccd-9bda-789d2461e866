<?php

namespace App\Http\Controllers;

use App\Models\Tenant;
use App\Models\SubscriptionPlan;
use App\Models\User;
use App\Services\FeatureGateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class SuperAdminController extends Controller
{
    protected FeatureGateService $featureGate;

    public function __construct(FeatureGateService $featureGate)
    {
        $this->featureGate = $featureGate;
    }

    /**
     * Super admin dashboard
     */
    public function dashboard()
    {
        $stats = [
            'total_tenants' => Tenant::count(),
            'active_tenants' => Tenant::where('status', 'active')->count(),
            'trial_tenants' => Tenant::where('subscription_status', 'trialing')->count(),
            'expired_tenants' => Tenant::where('status', 'expired')->count(),
            'total_revenue' => $this->calculateTotalRevenue(),
            'monthly_revenue' => $this->calculateMonthlyRevenue(),
        ];

        $recentTenants = Tenant::with('subscriptionPlan')
                              ->latest()
                              ->limit(10)
                              ->get();

        $planDistribution = Tenant::join('subscription_plans', 'tenants.subscription_plan_id', '=', 'subscription_plans.id')
                                 ->select('subscription_plans.name', DB::raw('count(*) as count'))
                                 ->groupBy('subscription_plans.name')
                                 ->get();

        return view('superadmin.dashboard', compact('stats', 'recentTenants', 'planDistribution'));
    }

    /**
     * List all tenants
     */
    public function tenants(Request $request)
    {
        $query = Tenant::with(['subscriptionPlan', 'users']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('subscription_status')) {
            $query->where('subscription_status', $request->subscription_status);
        }

        if ($request->filled('plan')) {
            $query->where('subscription_plan_id', $request->plan);
        }

        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('domain', 'like', '%' . $request->search . '%')
                  ->orWhere('slug', 'like', '%' . $request->search . '%');
            });
        }

        $tenants = $query->paginate(20);
        $plans = SubscriptionPlan::all();

        return view('superadmin.tenants.index', compact('tenants', 'plans'));
    }

    /**
     * Show tenant details
     */
    public function showTenant(Tenant $tenant)
    {
        $tenant->load(['subscriptionPlan', 'users', 'usage']);
        $usage = $this->featureGate->getAllUsageLimits($tenant);
        
        // Get tenant statistics
        $stats = [
            'total_contacts' => $tenant->contacts()->count(),
            'total_campaigns' => $tenant->campaigns()->count(),
            'total_messages' => $tenant->chatMessages()->count(),
            'active_bots' => $tenant->messageBots()->where('status', 'active')->count(),
        ];

        return view('superadmin.tenants.show', compact('tenant', 'usage', 'stats'));
    }

    /**
     * Create new tenant
     */
    public function createTenant()
    {
        $plans = SubscriptionPlan::active()->get();
        return view('superadmin.tenants.create', compact('plans'));
    }

    /**
     * Store new tenant
     */
    public function storeTenant(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:tenants,slug',
            'domain' => 'nullable|string|max:255|unique:tenants,domain',
            'subscription_plan_id' => 'required|exists:subscription_plans,id',
            'owner_email' => 'required|email',
            'owner_firstname' => 'required|string|max:255',
            'owner_lastname' => 'required|string|max:255',
        ]);

        DB::transaction(function () use ($request) {
            // Create tenant
            $tenant = Tenant::create([
                'name' => $request->name,
                'slug' => $request->slug,
                'domain' => $request->domain,
                'status' => 'active',
                'subscription_plan_id' => $request->subscription_plan_id,
                'subscription_status' => 'active',
                'trial_ends_at' => null,
            ]);

            // Create owner user
            $owner = User::create([
                'firstname' => $request->owner_firstname,
                'lastname' => $request->owner_lastname,
                'email' => $request->owner_email,
                'password' => bcrypt(Str::random(12)), // Generate random password
                'is_admin' => true,
                'active' => true,
            ]);

            // Associate user with tenant
            $tenant->users()->attach($owner->id, ['role' => 'owner']);
        });

        return redirect()->route('superadmin.tenants.index')
                        ->with('success', 'Tenant created successfully!');
    }

    /**
     * Edit tenant
     */
    public function editTenant(Tenant $tenant)
    {
        $plans = SubscriptionPlan::active()->get();
        return view('superadmin.tenants.edit', compact('tenant', 'plans'));
    }

    /**
     * Update tenant
     */
    public function updateTenant(Request $request, Tenant $tenant)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:tenants,slug,' . $tenant->id,
            'domain' => 'nullable|string|max:255|unique:tenants,domain,' . $tenant->id,
            'status' => 'required|in:active,suspended,trial,expired',
            'subscription_plan_id' => 'required|exists:subscription_plans,id',
            'subscription_status' => 'required|in:active,cancelled,past_due,trialing',
        ]);

        $tenant->update($request->only([
            'name', 'slug', 'domain', 'status', 
            'subscription_plan_id', 'subscription_status'
        ]));

        return redirect()->route('superadmin.tenants.show', $tenant)
                        ->with('success', 'Tenant updated successfully!');
    }

    /**
     * Suspend tenant
     */
    public function suspendTenant(Tenant $tenant)
    {
        $tenant->update(['status' => 'suspended']);
        
        return back()->with('success', 'Tenant suspended successfully!');
    }

    /**
     * Activate tenant
     */
    public function activateTenant(Tenant $tenant)
    {
        $tenant->update(['status' => 'active']);
        
        return back()->with('success', 'Tenant activated successfully!');
    }

    /**
     * Delete tenant
     */
    public function deleteTenant(Tenant $tenant)
    {
        $tenant->delete();
        
        return redirect()->route('superadmin.tenants.index')
                        ->with('success', 'Tenant deleted successfully!');
    }

    /**
     * Impersonate tenant (login as tenant owner)
     */
    public function impersonateTenant(Tenant $tenant)
    {
        $owner = $tenant->owner();
        
        if (!$owner) {
            return back()->with('error', 'No owner found for this tenant.');
        }

        // Store original user ID for returning later
        session(['impersonating_from' => Auth::id()]);
        session(['impersonating_tenant' => $tenant->id]);
        
        Auth::login($owner);
        
        return redirect()->route('dashboard')
                        ->with('success', 'Now impersonating ' . $tenant->name);
    }

    /**
     * Stop impersonation
     */
    public function stopImpersonation()
    {
        $originalUserId = session('impersonating_from');
        
        if ($originalUserId) {
            $originalUser = User::find($originalUserId);
            
            if ($originalUser) {
                Auth::login($originalUser);
                session()->forget(['impersonating_from', 'impersonating_tenant']);
                
                return redirect()->route('superadmin.dashboard')
                                ->with('success', 'Stopped impersonation');
            }
        }
        
        return redirect()->route('login');
    }

    /**
     * System analytics
     */
    public function analytics()
    {
        $analytics = [
            'tenant_growth' => $this->getTenantGrowthData(),
            'revenue_growth' => $this->getRevenueGrowthData(),
            'plan_popularity' => $this->getPlanPopularityData(),
            'usage_statistics' => $this->getUsageStatistics(),
        ];

        return view('superadmin.analytics', compact('analytics'));
    }

    /**
     * Calculate total revenue
     */
    private function calculateTotalRevenue(): float
    {
        // This would integrate with actual billing records
        return Tenant::join('subscription_plans', 'tenants.subscription_plan_id', '=', 'subscription_plans.id')
                    ->where('tenants.subscription_status', 'active')
                    ->sum('subscription_plans.price');
    }

    /**
     * Calculate monthly revenue
     */
    private function calculateMonthlyRevenue(): float
    {
        // This would calculate based on actual billing cycles
        return $this->calculateTotalRevenue(); // Simplified for demo
    }

    /**
     * Get tenant growth data for charts
     */
    private function getTenantGrowthData(): array
    {
        $months = [];
        $counts = [];
        
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $months[] = $date->format('M Y');
            $counts[] = Tenant::whereYear('created_at', $date->year)
                             ->whereMonth('created_at', $date->month)
                             ->count();
        }
        
        return ['months' => $months, 'counts' => $counts];
    }

    /**
     * Get revenue growth data
     */
    private function getRevenueGrowthData(): array
    {
        // Simplified demo data
        $months = [];
        $revenue = [];
        
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $months[] = $date->format('M Y');
            $revenue[] = rand(10000, 50000); // Demo data
        }
        
        return ['months' => $months, 'revenue' => $revenue];
    }

    /**
     * Get plan popularity data
     */
    private function getPlanPopularityData(): array
    {
        return Tenant::join('subscription_plans', 'tenants.subscription_plan_id', '=', 'subscription_plans.id')
                    ->select('subscription_plans.name', DB::raw('count(*) as count'))
                    ->groupBy('subscription_plans.name')
                    ->pluck('count', 'name')
                    ->toArray();
    }

    /**
     * Get usage statistics
     */
    private function getUsageStatistics(): array
    {
        return [
            'total_contacts' => DB::table('contacts')->count(),
            'total_campaigns' => DB::table('campaigns')->count(),
            'total_messages' => DB::table('chat_messages')->count(),
            'active_bots' => DB::table('message_bots')->where('status', 'active')->count(),
        ];
    }
}

