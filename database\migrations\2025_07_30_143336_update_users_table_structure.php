<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add missing columns that don't exist
            if (!Schema::hasColumn('users', 'firstname')) {
                $table->string('firstname')->after('id');
            }
            if (!Schema::hasColumn('users', 'lastname')) {
                $table->string('lastname')->after('firstname');
            }
            if (!Schema::hasColumn('users', 'phone')) {
                $table->string('phone')->nullable()->after('lastname');
            }
            if (!Schema::hasColumn('users', 'default_language')) {
                $table->string('default_language')->nullable()->after('phone');
            }
            if (!Schema::hasColumn('users', 'profile_image_url')) {
                $table->string('profile_image_url')->nullable()->after('default_language');
            }
            if (!Schema::hasColumn('users', 'role_id')) {
                $table->unsignedBigInteger('role_id')->nullable()->after('password');
            }
            if (!Schema::hasColumn('users', 'is_admin')) {
                $table->boolean('is_admin')->default(0)->after('role_id');
            }
            if (!Schema::hasColumn('users', 'banned_at')) {
                $table->string('banned_at')->nullable()->after('updated_at');
            }
            if (!Schema::hasColumn('users', 'send_welcome_mail')) {
                $table->boolean('send_welcome_mail')->default(1)->after('banned_at');
            }
            if (!Schema::hasColumn('users', 'active')) {
                $table->boolean('active')->default(1)->after('send_welcome_mail');
            }
            
            // Add indexes
            $table->index(['role_id']);
            $table->index(['is_admin']);
            $table->index(['active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['role_id']);
            $table->dropIndex(['is_admin']); 
            $table->dropIndex(['active']);
            $table->dropColumn([
                'firstname', 'lastname', 'phone', 'default_language', 
                'profile_image_url', 'role_id', 'is_admin', 'banned_at',
                'send_welcome_mail', 'active'
            ]);
        });
    }
};
