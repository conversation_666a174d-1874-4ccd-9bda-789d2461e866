{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "blade-ui-kit/blade-heroicons": "^2.5", "blade-ui-kit/blade-icons": "^1.7", "brunocfalcao/blade-feather-icons": "^4.0", "codeat3/blade-carbon-icons": "^2.32", "endroid/qr-code": "^6.0", "ezyang/htmlpurifier": "^4.18", "knuckleswtf/scribe": "^4.40", "laravel/breeze": "^2.3", "laravel/framework": "^11.31", "laravel/pint": "^1.20", "laravel/tinker": "^2.9", "laravolt/avatar": "^6.0", "league/csv": "^9.21", "livewire/livewire": "^3.5", "netflie/whatsapp-cloud-api": "^2.2", "openspout/openspout": "^4.28", "pharaonic/livewire-select2": "^1.2", "power-components/livewire-powergrid": "^6.1", "predis/predis": "2.0", "pusher/pusher-php-server": "^7.2", "spatie/laravel-permission": "^6.10", "spatie/laravel-settings": "^3.4", "theodo-group/llphant": "^0.9.3", "timehunter/laravel-google-recaptcha-v3": "^2.5", "wikimedia/composer-merge-plugin": "*"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.14", "barryvdh/laravel-ide-helper": "^3.5", "fakerphp/faker": "^1.23", "pestphp/pest": "^3.7", "pestphp/pest-plugin-laravel": "^3.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "@php artisan vendor:publish --tag=livewire:assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "clear-all": ["@php artisan clear-compiled", "@php artisan cache:clear", "@php artisan route:clear", "@php artisan view:clear", "@php artisan config:clear", "@php artisan cache:forget spatie.permission.cache", "@php artisan permission:cache-reset", "@php artisan icons:cache"], "pint": "./vendor/bin/pint"}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "wikimedia/composer-merge-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}