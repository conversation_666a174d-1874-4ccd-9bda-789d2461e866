<?php

namespace App\Services;

use App\Models\Tenant;
use App\Models\TenantUsage;

class FeatureGateService
{
    /**
     * Check if tenant has access to a specific feature
     */
    public function hasFeature(Tenant $tenant, string $feature): bool
    {
        if (!$tenant->subscriptionPlan) {
            return false;
        }

        return $tenant->subscriptionPlan->hasFeature($feature);
    }

    /**
     * Check if tenant is within usage limits for a resource
     */
    public function withinUsageLimit(Tenant $tenant, string $resource): bool
    {
        if (!$tenant->subscriptionPlan) {
            return false;
        }

        $limit = $tenant->subscriptionPlan->getLimit($resource);
        
        // Unlimited resources (limit = -1 or 0 means unlimited)
        if ($limit <= 0) {
            return true;
        }

        $currentUsage = $tenant->getCurrentUsage($resource);
        
        return $currentUsage < $limit;
    }

    /**
     * Get remaining usage for a resource
     */
    public function getRemainingUsage(Tenant $tenant, string $resource): int
    {
        if (!$tenant->subscriptionPlan) {
            return 0;
        }

        $limit = $tenant->subscriptionPlan->getLimit($resource);
        
        // Unlimited resources
        if ($limit <= 0) {
            return -1; // -1 indicates unlimited
        }

        $currentUsage = $tenant->getCurrentUsage($resource);
        
        return max(0, $limit - $currentUsage);
    }

    /**
     * Get usage percentage for a resource
     */
    public function getUsagePercentage(Tenant $tenant, string $resource): float
    {
        if (!$tenant->subscriptionPlan) {
            return 100.0;
        }

        $limit = $tenant->subscriptionPlan->getLimit($resource);
        
        // Unlimited resources
        if ($limit <= 0) {
            return 0.0;
        }

        $currentUsage = $tenant->getCurrentUsage($resource);
        
        return min(100.0, ($currentUsage / $limit) * 100);
    }

    /**
     * Track usage for a resource
     */
    public function trackUsage(Tenant $tenant, string $resource, int $amount = 1): void
    {
        TenantUsage::incrementUsage($tenant->id, $resource, $amount);
    }

    /**
     * Check if tenant can perform an action (has feature and within limits)
     */
    public function canPerformAction(Tenant $tenant, string $feature, string $resource = null): bool
    {
        // Check feature access
        if (!$this->hasFeature($tenant, $feature)) {
            return false;
        }

        // Check usage limits if resource is specified
        if ($resource && !$this->withinUsageLimit($tenant, $resource)) {
            return false;
        }

        return true;
    }

    /**
     * Get all usage limits for a tenant
     */
    public function getAllUsageLimits(Tenant $tenant): array
    {
        if (!$tenant->subscriptionPlan) {
            return [];
        }

        $limits = $tenant->subscriptionPlan->limits;
        $usage = [];

        foreach ($limits as $resource => $limit) {
            $currentUsage = $tenant->getCurrentUsage($resource);
            
            $usage[$resource] = [
                'limit' => $limit,
                'current' => $currentUsage,
                'remaining' => $limit > 0 ? max(0, $limit - $currentUsage) : -1,
                'percentage' => $limit > 0 ? min(100, ($currentUsage / $limit) * 100) : 0,
                'unlimited' => $limit <= 0
            ];
        }

        return $usage;
    }

    /**
     * Check if tenant is approaching usage limits (80% threshold)
     */
    public function isApproachingLimits(Tenant $tenant, float $threshold = 80.0): array
    {
        $approaching = [];
        $usage = $this->getAllUsageLimits($tenant);

        foreach ($usage as $resource => $data) {
            if (!$data['unlimited'] && $data['percentage'] >= $threshold) {
                $approaching[$resource] = $data;
            }
        }

        return $approaching;
    }

    /**
     * Get feature comparison between plans
     */
    public function compareFeatures(array $planIds): array
    {
        $plans = \App\Models\SubscriptionPlan::whereIn('id', $planIds)->get();
        $comparison = [];

        foreach ($plans as $plan) {
            $comparison[$plan->id] = [
                'name' => $plan->name,
                'price' => $plan->price,
                'features' => $plan->features,
                'limits' => $plan->limits
            ];
        }

        return $comparison;
    }
}

