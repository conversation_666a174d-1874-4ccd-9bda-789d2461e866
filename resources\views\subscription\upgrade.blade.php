@extends('layouts.app')

@section('title', 'Upgrade Subscription')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Upgrade Your Subscription</h1>
            <p class="text-lg text-gray-600">Choose a plan that fits your growing business needs</p>
        </div>

        <!-- Current Plan -->
        @if($currentPlan)
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Current Plan</h3>
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-700">{{ $currentPlan->name }}</p>
                    <p class="text-sm text-gray-500">{{ $currentPlan->formatted_price }} / {{ $currentPlan->billing_cycle }}</p>
                </div>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    {{ ucfirst($currentTenant->subscription_status) }}
                </span>
            </div>
        </div>
        @endif

        <!-- Available Upgrades -->
        <div class="space-y-6">
            @forelse($plans as $plan)
            <div class="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h3 class="text-xl font-semibold text-gray-900">{{ $plan->name }}</h3>
                            <p class="text-gray-600">{{ $plan->description }}</p>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold text-gray-900">{{ $plan->formatted_price }}</div>
                            <div class="text-sm text-gray-500">/{{ $plan->billing_cycle }}</div>
                        </div>
                    </div>

                    <!-- Features Comparison -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                        @foreach($plan->features as $feature => $value)
                        <div class="text-center">
                            <div class="text-sm font-medium text-gray-700 capitalize">{{ str_replace('_', ' ', $feature) }}</div>
                            <div class="text-lg font-semibold text-blue-600">
                                @if(is_bool($value))
                                    {{ $value ? '✓' : '✗' }}
                                @elseif($value == -1)
                                    ∞
                                @else
                                    {{ $value }}
                                @endif
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- Upgrade Form -->
                    <form action="{{ route('subscription.process-upgrade') }}" method="POST" class="space-y-4">
                        @csrf
                        <input type="hidden" name="plan_id" value="{{ $plan->id }}">
                        
                        <!-- Billing Cycle Selection -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Billing Cycle</label>
                            <div class="grid grid-cols-2 gap-4">
                                <label class="relative">
                                    <input type="radio" name="billing_cycle" value="monthly" class="sr-only" checked>
                                    <div class="border border-gray-300 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition duration-200 billing-option">
                                        <div class="text-center">
                                            <div class="font-semibold">Monthly</div>
                                            <div class="text-sm text-gray-500">{{ $plan->formatted_price }}/month</div>
                                        </div>
                                    </div>
                                </label>
                                
                                <label class="relative">
                                    <input type="radio" name="billing_cycle" value="yearly" class="sr-only">
                                    <div class="border border-gray-300 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition duration-200 billing-option">
                                        <div class="text-center">
                                            <div class="font-semibold">Yearly</div>
                                            <div class="text-sm text-gray-500">₹{{ number_format($plan->yearly_price, 2) }}/year</div>
                                            <div class="text-xs text-green-600 font-medium">Save 2 months!</div>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- Payment Summary -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Payment Summary</h4>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700">Total Amount:</span>
                                <span class="font-semibold text-lg" id="total-amount">{{ $plan->formatted_price }}</span>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex space-x-4">
                            <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold transition duration-200">
                                Upgrade Now
                            </button>
                            <a href="{{ route('subscription.plans') }}" class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-3 px-6 rounded-lg font-semibold text-center transition duration-200">
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            @empty
            <div class="text-center py-12">
                <div class="text-gray-500 text-lg">No upgrade options available</div>
                <p class="text-gray-400 mt-2">You're already on the highest plan!</p>
                <a href="{{ route('subscription.plans') }}" class="inline-block mt-4 text-blue-600 hover:text-blue-700">
                    View all plans
                </a>
            </div>
            @endforelse
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const billingOptions = document.querySelectorAll('.billing-option');
    const totalAmount = document.getElementById('total-amount');
    
    billingOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove selected class from all options
            billingOptions.forEach(opt => opt.classList.remove('border-blue-500', 'bg-blue-50'));
            
            // Add selected class to clicked option
            this.classList.add('border-blue-500', 'bg-blue-50');
            
            // Update total amount based on selection
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
            
            // Update total amount display (you can add more complex logic here)
            const cycle = radio.value;
            // Update amount based on cycle selection
        });
    });
    
    // Set initial selection
    document.querySelector('input[name="billing_cycle"]:checked').closest('.billing-option').classList.add('border-blue-500', 'bg-blue-50');
});
</script>
@endsection

