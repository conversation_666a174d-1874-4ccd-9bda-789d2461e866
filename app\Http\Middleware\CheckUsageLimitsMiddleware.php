<?php

namespace App\Http\Middleware;

use App\Models\Tenant;
use App\Services\FeatureGateService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckUsageLimitsMiddleware
{
    protected FeatureGateService $featureGate;

    public function __construct(FeatureGateService $featureGate)
    {
        $this->featureGate = $featureGate;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $resource = null): Response
    {
        $tenant = app('tenant');
        
        if (!$tenant) {
            return response()->json(['error' => 'Tenant not found'], 404);
        }

        // If no specific resource is provided, try to determine from route
        if (!$resource) {
            $resource = $this->determineResourceFromRoute($request);
        }

        if ($resource && !$this->featureGate->withinUsageLimit($tenant, $resource)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Usage limit exceeded',
                    'resource' => $resource,
                    'current_usage' => $tenant->getCurrentUsage($resource),
                    'limit' => $tenant->subscriptionPlan?->getLimit($resource) ?? 0
                ], 429);
            }
            
            return redirect()->route('subscription.upgrade')
                           ->with('error', "You've reached your {$resource} limit. Please upgrade your plan.");
        }

        return $next($request);
    }

    /**
     * Determine resource type from route
     */
    private function determineResourceFromRoute(Request $request): ?string
    {
        $route = $request->route();
        
        if (!$route) {
            return null;
        }

        $routeName = $route->getName();
        
        // Map route patterns to resources
        $resourceMap = [
            'contacts' => 'contacts',
            'campaigns' => 'campaigns',
            'templates' => 'templates',
            'bots' => 'bots',
            'messages' => 'messages',
        ];

        foreach ($resourceMap as $pattern => $resource) {
            if (str_contains($routeName, $pattern)) {
                return $resource;
            }
        }

        return null;
    }
}

