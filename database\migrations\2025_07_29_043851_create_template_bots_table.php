<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('template_bots', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('triggers'); // What triggers this template bot
            $table->unsignedBigInteger('template_id')->nullable(); // Link to WhatsApp template
            $table->json('template_parameters')->nullable(); // Parameters for the template
            $table->string('trigger_type')->default('keyword'); // keyword, webhook, schedule
            $table->boolean('is_active')->default(true);
            $table->integer('usage_count')->default(0);
            $table->json('conditions')->nullable(); // Conditions to check
            $table->integer('priority')->default(0);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            
            $table->index(['is_active']);
            $table->index(['trigger_type']);
            $table->index(['template_id']);
            $table->index(['priority']);
            $table->index(['created_by']);
            $table->foreign('template_id')->references('id')->on('whatsapp_templates')->onDelete('set null');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('template_bots');
    }
};
