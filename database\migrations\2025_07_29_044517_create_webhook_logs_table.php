<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('webhook_logs', function (Blueprint $table) {
            $table->id();
            $table->string('event');
            $table->string('model');
            $table->string('url');
            $table->string('status');
            $table->integer('attempt')->default(1);
            $table->json('payload');
            $table->json('response')->nullable();
            $table->text('error_message')->nullable();
            $table->integer('status_code')->nullable();
            $table->string('job_id')->nullable();
            $table->unsignedBigInteger('entity_id')->nullable();
            $table->timestamps();
            
            $table->index(['event']);
            $table->index(['model']);
            $table->index(['status']);
            $table->index(['entity_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('webhook_logs');
    }
};
