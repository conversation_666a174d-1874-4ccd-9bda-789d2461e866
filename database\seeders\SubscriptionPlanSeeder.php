<?php

namespace Database\Seeders;

use App\Models\SubscriptionPlan;
use Illuminate\Database\Seeder;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = [
            [
                'name' => 'Free',
                'slug' => 'free',
                'description' => 'Basic plan for individuals getting started with WhatsApp',
                'price' => 0.00,
                'billing_cycle' => 'monthly',
                'features' => [
                    'contacts' => 50,
                    'template_bots' => 5,
                    'message_bots' => 5,
                    'campaigns' => 5,
                    'ai_prompts' => 5,
                    'canned_replies' => 5,
                    'staff' => 1,
                    'conversations' => 50,
                    'bot_flow' => 10,
                    'api_access' => true,
                    'webhook_access' => true
                ],
                'limits' => [
                    'contacts' => 50,
                    'template_bots' => 5,
                    'message_bots' => 5,
                    'campaigns' => 5,
                    'ai_prompts' => 5,
                    'canned_replies' => 5,
                    'staff' => 1,
                    'conversations' => 50,
                    'bot_flows' => 10,
                    'monthly_messages' => 1000
                ],
                'is_active' => true
            ],
            [
                'name' => 'Plan M1 - Starter',
                'slug' => 'plan-m1-starter',
                'description' => 'Ideal for individuals starting out with essential messaging tools',
                'price' => 10.00,
                'billing_cycle' => 'monthly',
                'features' => [
                    'contacts' => 100,
                    'template_bots' => 100,
                    'message_bots' => 100,
                    'campaigns' => 100,
                    'ai_prompts' => 100,
                    'canned_replies' => 100,
                    'staff' => 100,
                    'conversations' => -1, // unlimited
                    'bot_flow' => 50,
                    'api_access' => true,
                    'webhook_access' => true
                ],
                'limits' => [
                    'contacts' => 100,
                    'template_bots' => 100,
                    'message_bots' => 100,
                    'campaigns' => 100,
                    'ai_prompts' => 100,
                    'canned_replies' => 100,
                    'staff' => 100,
                    'conversations' => -1, // unlimited
                    'bot_flows' => 50,
                    'monthly_messages' => 10000
                ],
                'is_active' => true
            ],
            [
                'name' => 'Plan M2 - Growth',
                'slug' => 'plan-m2-growth',
                'description' => 'Designed for growing teams needing more automation and capacity',
                'price' => 20.00,
                'billing_cycle' => 'monthly',
                'features' => [
                    'contacts' => 200,
                    'template_bots' => 200,
                    'message_bots' => 200,
                    'campaigns' => 200,
                    'ai_prompts' => 200,
                    'canned_replies' => 200,
                    'staff' => 200,
                    'conversations' => -1, // unlimited
                    'bot_flow' => 30,
                    'api_access' => true,
                    'webhook_access' => true
                ],
                'limits' => [
                    'contacts' => 200,
                    'template_bots' => 200,
                    'message_bots' => 200,
                    'campaigns' => 200,
                    'ai_prompts' => 200,
                    'canned_replies' => 200,
                    'staff' => 200,
                    'conversations' => -1, // unlimited
                    'bot_flows' => 30,
                    'monthly_messages' => 25000
                ],
                'is_active' => true
            ]
        ];

        foreach ($plans as $planData) {
            SubscriptionPlan::updateOrCreate(
                ['slug' => $planData['slug']],
                $planData
            );
        }

        $this->command->info('Subscription plans seeded successfully!');
    }
}

