{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "prepare": "husky", "format": "prettier --write \"resources/**/*.{js,vue,blade.php}\"", "format:check": "prettier --check \"resources/**/*.{js,vue,blade.php}\"", "lint-staged": "lint-staged"}, "lint-staged": {"resources/**/*.{js,vue}": ["prettier --write"], "resources/**/*.blade.php": ["prettier --write"]}, "devDependencies": {"@shufo/prettier-plugin-blade": "^1.15.3", "@tailwindcss/forms": "^0.5.2", "autoprefixer": "^10.4.2", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-echo": "^1.19.0", "laravel-vite-plugin": "^1.0", "lint-staged": "^15.5.0", "postcss": "^8.4.31", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "pusher-js": "^8.4.0", "quill": "^2.0.3", "tailwindcss": "^3.1.0", "vite": "^6.0"}, "dependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/typography": "github:tailwindcss/typography", "apexcharts": "^4.5.0", "dompurify": "^3.2.4", "emoji-mart": "^5.6.0", "flatpickr": "^4.6.13", "glightbox": "^3.3.1", "html2canvas": "^1.4.1", "husky": "^9.1.7", "intl-tel-input": "^25.2.1", "pretty-print-json": "^3.0.4", "recorder-core": "^1.3.25011100", "tippy.js": "^6.3.7", "tom-select": "^2.4.3", "tributejs": "^5.1.3"}}