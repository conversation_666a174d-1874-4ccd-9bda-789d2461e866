@extends('layouts.app')

@section('title', 'Subscription Plans')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Choose Your Plan</h1>
            <p class="text-xl text-gray-600">Scale your WhatsApp marketing with the perfect plan for your business</p>
        </div>

        <!-- Current Plan Status -->
        @if($currentPlan)
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-blue-900">Current Plan: {{ $currentPlan->name }}</h3>
                    <p class="text-blue-700">{{ $currentPlan->formatted_price }} / {{ $currentPlan->billing_cycle }}</p>
                    @if($currentTenant->isInTrial())
                        <p class="text-sm text-blue-600 mt-1">
                            Trial ends: {{ $currentTenant->trial_ends_at->format('M d, Y') }}
                        </p>
                    @endif
                </div>
                <div class="text-right">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                        @if($currentTenant->subscription_status === 'active') bg-green-100 text-green-800
                        @elseif($currentTenant->subscription_status === 'trialing') bg-yellow-100 text-yellow-800
                        @else bg-red-100 text-red-800 @endif">
                        {{ ucfirst($currentTenant->subscription_status) }}
                    </span>
                </div>
            </div>
        </div>
        @endif

        <!-- Usage Overview -->
        @if(!empty($usage))
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Current Usage</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                @foreach($usage as $resource => $data)
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700 capitalize">{{ str_replace('_', ' ', $resource) }}</span>
                        <span class="text-xs text-gray-500">
                            @if($data['unlimited'])
                                Unlimited
                            @else
                                {{ $data['current'] }}/{{ $data['limit'] }}
                            @endif
                        </span>
                    </div>
                    @if(!$data['unlimited'])
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ min(100, $data['percentage']) }}%"></div>
                    </div>
                    @endif
                </div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Pricing Plans -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            @foreach($plans as $plan)
            <div class="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden
                @if($currentPlan && $currentPlan->id === $plan->id) ring-2 ring-blue-500 @endif">
                
                <!-- Plan Header -->
                <div class="px-6 py-8 text-center
                    @if($plan->slug === 'free') bg-gray-50
                    @elseif($plan->slug === 'plan-m1-starter') bg-blue-50
                    @else bg-purple-50 @endif">
                    
                    <h3 class="text-2xl font-bold text-gray-900">{{ $plan->name }}</h3>
                    <p class="text-gray-600 mt-2">{{ $plan->description }}</p>
                    
                    <div class="mt-4">
                        <span class="text-4xl font-bold text-gray-900">{{ $plan->formatted_price }}</span>
                        @if($plan->price > 0)
                            <span class="text-gray-600">/{{ $plan->billing_cycle }}</span>
                        @endif
                    </div>
                </div>

                <!-- Features List -->
                <div class="px-6 py-6">
                    <ul class="space-y-3">
                        @foreach($plan->features as $feature => $value)
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-gray-700">
                                {{ ucfirst(str_replace('_', ' ', $feature)) }}: 
                                @if(is_bool($value))
                                    {{ $value ? 'Yes' : 'No' }}
                                @elseif($value == -1)
                                    Unlimited
                                @else
                                    {{ $value }}
                                @endif
                            </span>
                        </li>
                        @endforeach
                    </ul>
                </div>

                <!-- Action Button -->
                <div class="px-6 py-6 bg-gray-50">
                    @if($currentPlan && $currentPlan->id === $plan->id)
                        <button disabled class="w-full bg-gray-300 text-gray-500 py-3 px-4 rounded-lg font-semibold">
                            Current Plan
                        </button>
                    @elseif(!$currentPlan || $plan->price > $currentPlan->price)
                        <a href="{{ route('subscription.upgrade', ['plan' => $plan->id]) }}" 
                           class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-semibold text-center block transition duration-200">
                            @if($plan->price == 0) Get Started @else Upgrade @endif
                        </a>
                    @else
                        <button disabled class="w-full bg-gray-300 text-gray-500 py-3 px-4 rounded-lg font-semibold">
                            Lower Plan
                        </button>
                    @endif
                </div>
            </div>
            @endforeach
        </div>

        <!-- FAQ Section -->
        <div class="mt-16">
            <h2 class="text-3xl font-bold text-center text-gray-900 mb-8">Frequently Asked Questions</h2>
            <div class="max-w-3xl mx-auto space-y-6">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Can I change my plan anytime?</h3>
                    <p class="text-gray-600">Yes, you can upgrade your plan at any time. Downgrades will take effect at the end of your current billing cycle.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">What happens if I exceed my limits?</h3>
                    <p class="text-gray-600">You'll receive notifications when approaching limits. Once exceeded, certain features may be restricted until you upgrade or the next billing cycle.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Is there a free trial?</h3>
                    <p class="text-gray-600">Yes, all new accounts start with a 14-day free trial on our Starter plan. No credit card required.</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

