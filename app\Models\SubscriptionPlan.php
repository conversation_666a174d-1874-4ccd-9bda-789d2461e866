<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SubscriptionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'billing_cycle',
        'features',
        'limits',
        'is_active'
    ];

    protected $casts = [
        'features' => 'array',
        'limits' => 'array',
        'price' => 'decimal:2',
        'is_active' => 'boolean'
    ];

    /**
     * Get all tenants using this subscription plan
     */
    public function tenants(): HasMany
    {
        return $this->hasMany(Tenant::class);
    }

    /**
     * Get the formatted price with currency
     */
    public function getFormattedPriceAttribute(): string
    {
        return '₹' . number_format($this->price, 2);
    }

    /**
     * Get the billing cycle display name
     */
    public function getBillingCycleDisplayAttribute(): string
    {
        return ucfirst($this->billing_cycle);
    }

    /**
     * Check if plan has a specific feature
     */
    public function hasFeature(string $feature): bool
    {
        return isset($this->features[$feature]) && $this->features[$feature] > 0;
    }

    /**
     * Get the limit for a specific resource
     */
    public function getLimit(string $resource): int
    {
        return $this->limits[$resource] ?? 0;
    }

    /**
     * Get the feature value
     */
    public function getFeature(string $feature): mixed
    {
        return $this->features[$feature] ?? null;
    }

    /**
     * Scope to get active plans
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get plans by billing cycle
     */
    public function scopeBillingCycle($query, string $cycle)
    {
        return $query->where('billing_cycle', $cycle);
    }

    /**
     * Get plans ordered by price
     */
    public function scopeOrderByPrice($query, string $direction = 'asc')
    {
        return $query->orderBy('price', $direction);
    }

    /**
     * Check if this is a free plan
     */
    public function isFree(): bool
    {
        return $this->price == 0;
    }

    /**
     * Get the yearly equivalent price for monthly plans
     */
    public function getYearlyPriceAttribute(): float
    {
        if ($this->billing_cycle === 'yearly') {
            return $this->price;
        }
        
        return $this->price * 12;
    }

    /**
     * Get the monthly equivalent price for yearly plans
     */
    public function getMonthlyPriceAttribute(): float
    {
        if ($this->billing_cycle === 'monthly') {
            return $this->price;
        }
        
        return $this->price / 12;
    }
}

