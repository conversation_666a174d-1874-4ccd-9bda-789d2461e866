<?php

namespace App\Http\Middleware;

use App\Models\Tenant;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ResolveTenantMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip tenant resolution for certain routes
        if ($this->shouldSkipTenantResolution($request)) {
            return $next($request);
        }

        $tenant = $this->resolveTenant($request);
        
        if (!$tenant) {
            // For development, allow access without tenant for certain routes
            if (app()->environment(['local', 'testing']) && $this->isPublicRoute($request)) {
                // Use a default tenant for development
                $tenant = Tenant::first();
                if ($tenant) {
                    app()->instance('tenant', $tenant);
                    view()->share('currentTenant', $tenant);
                }
                return $next($request);
            }
            
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Tenant not found'], 404);
            }
            abort(404, 'Tenant not found');
        }

        // Check if tenant is active
        if ($tenant->status === 'suspended') {
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Tenant suspended'], 403);
            }
            return response()->view('errors.tenant-suspended', compact('tenant'), 403);
        }

        // Bind tenant to the application container
        app()->instance('tenant', $tenant);
        
        // Set tenant context for views
        view()->share('currentTenant', $tenant);
        
        return $next($request);
    }

    /**
     * Resolve tenant from request
     */
    private function resolveTenant(Request $request): ?Tenant
    {
        $host = $request->getHost();
        
        // First try to resolve by custom domain
        $tenant = Tenant::where('domain', $host)->first();
        
        if ($tenant) {
            return $tenant;
        }

        // Then try to resolve by subdomain
        $subdomain = $this->extractSubdomain($host);
        
        if ($subdomain && $subdomain !== 'www') {
            return Tenant::where('slug', $subdomain)->first();
        }

        // For development/testing, try to get tenant from header or session
        if (app()->environment(['local', 'testing'])) {
            $tenantId = $request->header('X-Tenant-ID') ?? session('tenant_id');
            if ($tenantId) {
                return Tenant::find($tenantId);
            }
            
            // For localhost development, use the first available tenant
            if (in_array($host, ['127.0.0.1', 'localhost'])) {
                return Tenant::first();
            }
        }

        return null;
    }

    /**
     * Extract subdomain from host
     */
    private function extractSubdomain(string $host): ?string
    {
        $parts = explode('.', $host);
        
        // If we have at least 3 parts (subdomain.domain.tld), return the first part
        if (count($parts) >= 3) {
            return $parts[0];
        }
        
        return null;
    }

    /**
     * Check if tenant resolution should be skipped for this request
     */
    private function shouldSkipTenantResolution(Request $request): bool
    {
        $skipRoutes = [
            'superadmin/*',
            'database-upgrade',
            'upgrade',
            'whatsapp/webhook',
            'up', // Health check
            'livewire/*',
            'storage/*',
        ];

        foreach ($skipRoutes as $pattern) {
            if ($request->is($pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if this is a public route that can work without tenant in development
     */
    private function isPublicRoute(Request $request): bool
    {
        $publicRoutes = [
            '/',
            'login',
            'register',
            'forgot-password',
            'reset-password/*',
            'verify-email/*',
            'admin',
            'admin/*',
        ];

        foreach ($publicRoutes as $pattern) {
            if ($request->is($pattern)) {
                return true;
            }
        }

        return false;
    }
}

