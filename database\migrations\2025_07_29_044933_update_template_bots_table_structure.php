<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop and recreate with correct structure
        Schema::dropIfExists('template_bots');
        
        Schema::create('template_bots', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('rel_type');
            $table->unsignedBigInteger('template_id')->nullable();
            $table->json('header_params')->nullable();
            $table->json('body_params')->nullable();
            $table->json('footer_params')->nullable();
            $table->string('filename')->nullable();
            $table->json('trigger')->nullable();
            $table->integer('reply_type');
            $table->boolean('is_bot_active')->default(1);
            $table->integer('sending_count')->default(0);
            $table->timestamps();
            
            $table->index(['rel_type']);
            $table->index(['template_id']);
            $table->index(['is_bot_active']);
            $table->index(['reply_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('template_bots');
    }
};
