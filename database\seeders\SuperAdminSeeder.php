<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $superAdminEmail = '<EMAIL>';
        
        // Create or update super admin user
        $superAdmin = User::updateOrCreate(
            ['email' => $superAdminEmail],
            [
                'firstname' => 'Super',
                'lastname' => 'Admin',
                'password' => Hash::make('12345678'),
                'is_admin' => true,
                'is_super_admin' => true,
                'active' => true,
                'email_verified_at' => now(),
            ]
        );

        $this->command->info('Super admin user created/updated successfully!');
        $this->command->info('Email: ' . $superAdminEmail);
        $this->command->info('Password: 12345678');
    }
}

