<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TenantUsage extends Model
{
    use HasFactory;

    protected $table = 'tenant_usage';

    protected $fillable = [
        'tenant_id',
        'resource',
        'period',
        'count'
    ];

    protected $casts = [
        'count' => 'integer'
    ];

    /**
     * Get the tenant that owns this usage record
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Increment usage count for a resource
     */
    public static function incrementUsage(int $tenantId, string $resource, int $amount = 1): void
    {
        $period = now()->format('Y-m');
        
        static::updateOrCreate([
            'tenant_id' => $tenantId,
            'resource' => $resource,
            'period' => $period
        ], [
            'count' => \DB::raw("count + {$amount}")
        ]);
    }

    /**
     * Get current usage for a tenant and resource
     */
    public static function getCurrentUsage(int $tenantId, string $resource): int
    {
        $period = now()->format('Y-m');
        
        return static::where('tenant_id', $tenantId)
                    ->where('resource', $resource)
                    ->where('period', $period)
                    ->value('count') ?? 0;
    }

    /**
     * Get usage for a specific period
     */
    public static function getUsageForPeriod(int $tenantId, string $resource, string $period): int
    {
        return static::where('tenant_id', $tenantId)
                    ->where('resource', $resource)
                    ->where('period', $period)
                    ->value('count') ?? 0;
    }

    /**
     * Get usage history for a tenant
     */
    public static function getUsageHistory(int $tenantId, int $months = 12): array
    {
        $periods = [];
        for ($i = 0; $i < $months; $i++) {
            $periods[] = now()->subMonths($i)->format('Y-m');
        }

        return static::where('tenant_id', $tenantId)
                    ->whereIn('period', $periods)
                    ->get()
                    ->groupBy('resource')
                    ->map(function ($usage) {
                        return $usage->keyBy('period');
                    })
                    ->toArray();
    }

    /**
     * Reset usage for a new billing period
     */
    public static function resetUsageForPeriod(int $tenantId, string $period): void
    {
        static::where('tenant_id', $tenantId)
              ->where('period', $period)
              ->delete();
    }

    /**
     * Scope to get usage for current period
     */
    public function scopeCurrentPeriod($query)
    {
        return $query->where('period', now()->format('Y-m'));
    }

    /**
     * Scope to get usage for a specific tenant
     */
    public function scopeForTenant($query, int $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    /**
     * Scope to get usage for a specific resource
     */
    public function scopeForResource($query, string $resource)
    {
        return $query->where('resource', $resource);
    }
}

