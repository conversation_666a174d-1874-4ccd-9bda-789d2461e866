<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('chat_messages', function (Blueprint $table) {
            // Drop existing columns that don't match the model
            $table->dropColumn(['chat_id', 'from_phone_number', 'to_phone_number', 'message_type', 'direction', 'metadata', 'message_timestamp']);
            
            // Add expected columns
            $table->unsignedBigInteger('interaction_id')->after('id');
            $table->string('sender_id')->after('interaction_id');
            $table->string('url')->nullable()->after('sender_id');
            $table->string('time_sent')->after('status');
            $table->string('staff_id')->nullable()->after('message_id');
            $table->string('type')->nullable()->after('staff_id');
            $table->string('ref_message_id')->nullable()->after('is_read');
            $table->string('status_message')->nullable()->after('ref_message_id');
            
            // Add indexes
            $table->index(['interaction_id']);
            $table->index(['sender_id']);
            $table->index(['time_sent']);
            $table->index(['staff_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('chat_messages', function (Blueprint $table) {
            // Reverse the changes
            $table->dropColumn(['interaction_id', 'sender_id', 'url', 'time_sent', 'staff_id', 'type', 'ref_message_id', 'status_message']);
            
            // Add back original columns
            $table->string('chat_id')->nullable();
            $table->string('from_phone_number')->nullable();
            $table->string('to_phone_number')->nullable();
            $table->string('message_type')->default('text');
            $table->string('direction')->default('inbound');
            $table->json('metadata')->nullable();
            $table->timestamp('message_timestamp')->nullable();
        });
    }
};
