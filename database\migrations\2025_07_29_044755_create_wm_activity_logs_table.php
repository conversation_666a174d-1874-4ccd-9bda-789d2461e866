<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wm_activity_logs', function (Blueprint $table) {
            $table->id();
            $table->string('phone_number_id')->nullable();
            $table->text('access_token')->nullable();
            $table->string('business_account_id')->nullable();
            $table->string('response_code');
            $table->unsignedBigInteger('client_id')->nullable();
            $table->text('response_data')->nullable();
            $table->string('category')->nullable();
            $table->unsignedBigInteger('category_id')->nullable();
            $table->string('rel_type')->nullable();
            $table->unsignedBigInteger('rel_id')->nullable();
            $table->text('category_params')->nullable();
            $table->longText('raw_data')->nullable();
            $table->string('recorded_at')->nullable();
            $table->timestamps();
            
            $table->index(['phone_number_id']);
            $table->index(['client_id']);
            $table->index(['category']);
            $table->index(['rel_type', 'rel_id']);
            $table->index(['response_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wm_activity_logs');
    }
};
