<?php

namespace App\Livewire\Admin\Settings\System;

// use Corbital\Installer\Classes\UpdateChecker; // Disabled for SaaS platform
use Livewire\Component;

class SystemUpdateSettings extends Component
{
    public $currentVersion;

    public $latestVersion;

    public $purchase_key;

    public $username;

    public $update_id;

    public $has_sql_update;

    public $releases = [];

    public $update = [];

    public $token;

    public $support = [];

    public $versionLog = [];

    protected $rules = [
        'purchase_key' => 'required|string',
        'username'     => 'required|string',
    ];

    public function mount()
    {
        if (! checkPermission('system_settings.view')) {
            $this->notify(['type' => 'danger', 'message' => t('access_denied_note')], true);

            return redirect(route('admin.dashboard'));
        }
        
        // For SaaS platform, use static version info
        $this->currentVersion = config('app.version', '2.0.0');
        $this->latestVersion = config('app.version', '2.0.0');
        $this->update = ['latest_version' => $this->currentVersion];
        $this->support = ['status' => 'active'];
    }

    public function loadReleases()
    {
        // Disabled for SaaS platform - no external updates needed
        $this->latestVersion = config('app.version', '2.0.0');
        $this->update_id = 0;
        $this->has_sql_update = false;
        $this->versionLog = [];
    }

    public function save()
    {
        // Disabled for SaaS platform - no external updates needed
        $this->notify(['type' => 'info', 'message' => 'Updates are managed automatically in SaaS platform']);
    }

    public function render()
    {
        return view('livewire.admin.settings.system.system-update-settings');
    }
}
