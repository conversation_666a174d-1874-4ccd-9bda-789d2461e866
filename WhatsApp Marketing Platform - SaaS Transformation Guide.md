# WhatsApp Marketing Platform - SaaS Transformation Guide

## Overview

This document provides a comprehensive guide for the transformation of your WhatsApp marketing platform into a multi-tenant SaaS solution, similar to WhatsMarkSaaS. The transformation includes multi-tenancy, subscription management, billing integration, and super admin functionality.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Key Features Implemented](#key-features-implemented)
3. [Database Schema Changes](#database-schema-changes)
4. [Installation & Setup](#installation--setup)
5. [Configuration](#configuration)
6. [Testing](#testing)
7. [Deployment](#deployment)
8. [API Documentation](#api-documentation)
9. [Troubleshooting](#troubleshooting)

## Architecture Overview

### Multi-Tenant Architecture

The platform now supports multiple tenants with complete data isolation:

- **Tenant Resolution**: Automatic tenant detection via subdomain or custom domain
- **Data Isolation**: All tenant data is automatically scoped using the TenantScoped trait
- **Resource Limits**: Per-tenant usage limits based on subscription plans
- **Feature Gating**: Subscription-based feature access control

### Core Components

1. **Tenant Management System**
   - Tenant model with subscription relationships
   - Automatic tenant resolution middleware
   - Data isolation through global scopes

2. **Subscription Management**
   - Multiple subscription plans with feature limits
   - Trial period management
   - Usage tracking and limit enforcement

3. **Billing System**
   - Multi-gateway payment processing (Stripe, Razorpay, Demo)
   - Subscription lifecycle management
   - Invoice generation and billing history

4. **Super Admin Panel**
   - Tenant management interface
   - Analytics and reporting
   - Tenant impersonation for support

## Key Features Implemented

### ✅ Multi-Tenancy Features
- Automatic tenant resolution via subdomain/domain
- Complete data isolation between tenants
- Tenant-specific user management
- Custom domain support

### ✅ Subscription Management
- Multiple subscription plans (Free, Starter, Growth)
- Feature-based access control
- Usage limit enforcement
- Trial period management
- Subscription upgrade/downgrade flows

### ✅ Billing Integration
- Multi-gateway support (Stripe, Razorpay, Demo)
- Automated billing cycles
- Invoice generation
- Payment failure handling
- Billing history tracking

### ✅ Super Admin Panel
- Comprehensive tenant management
- Real-time analytics and reporting
- Tenant impersonation functionality
- System health monitoring
- Bulk tenant operations

### ✅ Security Features
- Role-based access control
- Secure tenant impersonation
- Protected super admin routes
- Data isolation enforcement

## Database Schema Changes

### New Tables Added

1. **tenants** - Core tenant information
2. **subscription_plans** - Available subscription plans
3. **tenant_users** - Tenant-user relationships
4. **tenant_usage** - Usage tracking per tenant

### Modified Tables

All existing data tables now include:
- `tenant_id` column for data isolation
- Foreign key constraints to tenants table
- Updated indexes for performance

### Key Relationships

```
Tenant -> SubscriptionPlan (belongs to)
Tenant -> Users (many to many through tenant_users)
Tenant -> Contacts, Campaigns, etc. (has many with tenant_id)
```

## Installation & Setup

### Prerequisites

- PHP 8.1 or higher
- MySQL 8.0 or higher
- Composer
- Node.js and NPM

### Step 1: Environment Configuration

Copy the provided `.env.example` to `.env` and configure:

```bash
cp .env.example .env
```

Key configuration variables:
```env
# SaaS Configuration
SAAS_MODE=true
MULTI_TENANT=true
TENANT_DOMAIN_STRATEGY=subdomain

# Super Admin
SUPER_ADMIN_EMAILS="<EMAIL>"

# Billing
BILLING_GATEWAY=demo
BILLING_CURRENCY=INR
TRIAL_DAYS=14
```

### Step 2: Database Setup

Run migrations and seeders:

```bash
php artisan migrate
php artisan db:seed
```

This will create:
- All SaaS tables and relationships
- Default subscription plans
- Super admin user
- Demo tenants for testing

### Step 3: Application Key

Generate application key:

```bash
php artisan key:generate
```

### Step 4: Storage Setup

Create storage links:

```bash
php artisan storage:link
```

## Configuration

### Subscription Plans

Default plans are seeded automatically:

1. **Free Plan** - ₹0/month
   - 50 contacts, 5 bots, 1000 messages/month

2. **Starter Plan** - ₹10/month
   - 100 contacts, 100 bots, 10,000 messages/month

3. **Growth Plan** - ₹20/month
   - 200 contacts, 200 bots, 25,000 messages/month

### Payment Gateways

#### Demo Mode (Default)
```env
BILLING_GATEWAY=demo
DEMO_PAYMENTS=true
DEMO_ALWAYS_SUCCEED=true
```

#### Stripe Integration
```env
BILLING_GATEWAY=stripe
STRIPE_PUBLIC_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

#### Razorpay Integration
```env
BILLING_GATEWAY=razorpay
RAZORPAY_KEY_ID=rzp_test_...
RAZORPAY_KEY_SECRET=...
RAZORPAY_WEBHOOK_SECRET=...
```

### Domain Configuration

#### Subdomain Strategy (Recommended)
```env
TENANT_DOMAIN_STRATEGY=subdomain
APP_URL=https://yourapp.com
```

Tenants access via: `tenant-slug.yourapp.com`

#### Custom Domain Strategy
```env
TENANT_DOMAIN_STRATEGY=domain
```

Tenants can use custom domains: `tenant-domain.com`

## Testing

### Default Accounts

After seeding, you can test with these accounts:

#### Super Admin
- **URL**: `/superadmin/dashboard`
- **Email**: `<EMAIL>`
- **Password**: `********`

#### Demo Tenant
- **URL**: `demo.yourapp.com` (or `/login` with tenant context)
- **Email**: `<EMAIL>`
- **Password**: `********`

### Testing Scenarios

1. **Multi-Tenancy Testing**
   - Create multiple tenants via super admin
   - Verify data isolation between tenants
   - Test subdomain/domain resolution

2. **Subscription Testing**
   - Test plan upgrades/downgrades
   - Verify usage limit enforcement
   - Test trial period expiration

3. **Billing Testing**
   - Process demo payments
   - Test payment failures
   - Verify billing history

4. **Super Admin Testing**
   - Tenant management operations
   - Impersonation functionality
   - Analytics and reporting

## Deployment

### Production Checklist

1. **Environment Configuration**
   ```env
   APP_ENV=production
   APP_DEBUG=false
   SAAS_MODE=true
   ```

2. **Database Optimization**
   ```bash
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

3. **Security Setup**
   - Configure proper SSL certificates
   - Set up wildcard SSL for subdomains
   - Configure CORS policies
   - Set up rate limiting

4. **Payment Gateway Setup**
   - Configure production payment credentials
   - Set up webhook endpoints
   - Test payment flows

5. **Monitoring Setup**
   - Configure error tracking
   - Set up performance monitoring
   - Configure backup systems

### Server Requirements

- **Web Server**: Nginx or Apache with PHP-FPM
- **Database**: MySQL 8.0+ with proper indexing
- **Cache**: Redis for session and cache storage
- **Queue**: Redis or database for background jobs
- **Storage**: S3 or local storage for file uploads

### Scaling Considerations

1. **Database Scaling**
   - Implement read replicas
   - Consider database sharding for large tenant counts
   - Optimize queries with proper indexing

2. **Application Scaling**
   - Use load balancers for multiple app servers
   - Implement horizontal scaling
   - Use CDN for static assets

3. **Tenant Isolation**
   - Consider separate databases for enterprise tenants
   - Implement resource quotas
   - Monitor tenant resource usage

## API Documentation

### Tenant API Endpoints

All API endpoints are automatically tenant-scoped:

```
GET /api/contacts - Get tenant contacts
POST /api/campaigns - Create tenant campaign
GET /api/subscription/usage - Get usage statistics
```

### Super Admin API Endpoints

```
GET /superadmin/tenants - List all tenants
POST /superadmin/tenants - Create new tenant
PUT /superadmin/tenants/{id} - Update tenant
DELETE /superadmin/tenants/{id} - Delete tenant
```

### Subscription API Endpoints

```
GET /subscription/plans - List available plans
POST /subscription/upgrade - Process upgrade
GET /api/subscription/usage - Get usage data
POST /subscription/cancel - Cancel subscription
```

## Troubleshooting

### Common Issues

#### 1. Tenant Resolution Issues
**Problem**: Tenant not found or incorrect tenant resolved

**Solution**:
- Check subdomain configuration
- Verify tenant exists in database
- Check middleware order in bootstrap/app.php

#### 2. Data Isolation Problems
**Problem**: Seeing data from other tenants

**Solution**:
- Ensure TenantScoped trait is applied to all models
- Check global scope implementation
- Verify tenant_id is set correctly

#### 3. Subscription Limit Issues
**Problem**: Limits not enforced correctly

**Solution**:
- Check FeatureGateService implementation
- Verify usage tracking is working
- Check middleware application

#### 4. Payment Processing Issues
**Problem**: Payments failing or not processing

**Solution**:
- Check payment gateway configuration
- Verify webhook endpoints
- Check BillingService implementation

### Debug Commands

```bash
# Check tenant resolution
php artisan tinker
>>> app('tenant')

# Check current user's tenants
>>> auth()->user()->tenants

# Verify subscription limits
>>> app(FeatureGateService::class)->getAllUsageLimits(app('tenant'))
```

### Log Monitoring

Key log channels to monitor:
- `laravel.log` - General application logs
- `billing.log` - Payment processing logs
- `tenant.log` - Tenant-specific operations

## Support and Maintenance

### Regular Maintenance Tasks

1. **Database Cleanup**
   - Archive old tenant data
   - Clean up expired trials
   - Optimize database tables

2. **Usage Monitoring**
   - Monitor tenant resource usage
   - Track subscription metrics
   - Analyze growth patterns

3. **Security Updates**
   - Regular dependency updates
   - Security patch management
   - Access audit reviews

### Backup Strategy

1. **Database Backups**
   - Daily automated backups
   - Point-in-time recovery capability
   - Cross-region backup storage

2. **File Storage Backups**
   - Regular file system backups
   - Version control for configurations
   - Disaster recovery procedures

## Conclusion

Your WhatsApp marketing platform has been successfully transformed into a comprehensive SaaS solution with:

- Complete multi-tenancy support
- Subscription management system
- Billing integration
- Super admin functionality
- Enterprise-grade security

The platform is now ready for production deployment and can scale to support thousands of tenants with proper infrastructure setup.

For additional support or customization needs, refer to the codebase documentation or contact the development team.

