<?php

use App\Http\Controllers\SubscriptionController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'tenant.subscription'])->group(function () {
    Route::get('/subscription/plans', [SubscriptionController::class, 'plans'])->name('subscription.plans');
    Route::get('/subscription/upgrade', [SubscriptionController::class, 'upgrade'])->name('subscription.upgrade');
    Route::post('/subscription/upgrade', [SubscriptionController::class, 'processUpgrade'])->name('subscription.process-upgrade');
    Route::get('/subscription/success', [SubscriptionController::class, 'success'])->name('subscription.success');
    Route::post('/subscription/cancel', [SubscriptionController::class, 'cancel'])->name('subscription.cancel');
    Route::get('/subscription/billing', [SubscriptionController::class, 'billing'])->name('subscription.billing');
    Route::get('/api/subscription/usage', [SubscriptionController::class, 'usage'])->name('api.subscription.usage');
    Route::get('/api/subscription/compare', [SubscriptionController::class, 'compare'])->name('api.subscription.compare');
});

// Routes that don't require active subscription
Route::middleware(['auth'])->group(function () {
    Route::get('/subscription/expired', [SubscriptionController::class, 'expired'])->name('subscription.expired');
});

