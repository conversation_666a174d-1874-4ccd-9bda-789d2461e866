<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop foreign key constraints first
        Schema::table('contact_notes', function (Blueprint $table) {
            $table->dropForeign(['contact_id']);
        });
        
        // Drop and recreate with correct structure
        Schema::dropIfExists('contacts');
        
        Schema::create('contacts', function (Blueprint $table) {
            $table->id();
            $table->string('firstname');
            $table->string('lastname');
            $table->string('company')->nullable();
            $table->string('type');
            $table->text('description')->nullable();
            $table->unsignedBigInteger('country_id')->nullable();
            $table->string('zip')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->text('address')->nullable();
            $table->unsignedBigInteger('assigned_id')->nullable();
            $table->unsignedBigInteger('status_id');
            $table->unsignedBigInteger('source_id');
            $table->string('email')->nullable();
            $table->string('website')->nullable();
            $table->string('phone');
            $table->boolean('is_enabled')->default(1);
            $table->unsignedBigInteger('addedfrom');
            $table->string('dateassigned')->nullable();
            $table->string('last_status_change')->nullable();
            $table->string('default_language')->nullable();
            $table->timestamps();
            
            $table->index(['phone']);
            $table->index(['email']);
            $table->index(['status_id']);
            $table->index(['source_id']);
            $table->index(['assigned_id']);
            $table->index(['country_id']);
            $table->index(['is_enabled']);
            $table->index(['type']);
            $table->foreign('status_id')->references('id')->on('statuses')->onDelete('cascade');
            $table->foreign('source_id')->references('id')->on('sources')->onDelete('cascade');
        });
        
        // Recreate foreign key in contact_notes
        Schema::table('contact_notes', function (Blueprint $table) {
            $table->foreign('contact_id')->references('id')->on('contacts')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contacts');
    }
};
