<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $tables = [
            'contacts',
            'campaigns',
            'chat_messages',
            'templates',
            'whatsapp_templates',
            'message_bots',
            'template_bots',
            'canned_replies',
            'ai_prompts',
            'chat',
            'contact_notes',
            'campaign_details',
            'webhook_logs',
            'pusher_notifications',
            'wm_activity_logs'
        ];

        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                Schema::table($table, function (Blueprint $table_blueprint) use ($table) {
                    if (!Schema::hasColumn($table, 'tenant_id')) {
                        $table_blueprint->unsignedBigInteger('tenant_id')->after('id');
                        $table_blueprint->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
                        $table_blueprint->index(['tenant_id']);
                    }
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $tables = [
            'contacts',
            'campaigns',
            'chat_messages',
            'templates',
            'whatsapp_templates',
            'message_bots',
            'template_bots',
            'canned_replies',
            'ai_prompts',
            'chat',
            'contact_notes',
            'campaign_details',
            'webhook_logs',
            'pusher_notifications',
            'wm_activity_logs'
        ];

        foreach ($tables as $table) {
            if (Schema::hasTable($table) && Schema::hasColumn($table, 'tenant_id')) {
                Schema::table($table, function (Blueprint $table_blueprint) {
                    $table_blueprint->dropForeign(['tenant_id']);
                    $table_blueprint->dropIndex(['tenant_id']);
                    $table_blueprint->dropColumn('tenant_id');
                });
            }
        }
    }
};

