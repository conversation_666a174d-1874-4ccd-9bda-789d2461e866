<?php

namespace App\Http\Middleware;

use App\Models\Tenant;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ValidateSubscriptionMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $tenant = app('tenant');
        
        if (!$tenant) {
            return response()->json(['error' => 'Tenant not found'], 404);
        }

        // Check if tenant has a valid subscription
        if (!$this->hasValidSubscription($tenant)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Subscription expired or invalid',
                    'subscription_status' => $tenant->subscription_status,
                    'trial_ends_at' => $tenant->trial_ends_at
                ], 402);
            }
            
            return redirect()->route('subscription.expired');
        }

        return $next($request);
    }

    /**
     * Check if tenant has a valid subscription
     */
    private function hasValidSubscription(Tenant $tenant): bool
    {
        // Allow access during trial period
        if ($tenant->subscription_status === 'trialing' && 
            $tenant->trial_ends_at && 
            $tenant->trial_ends_at->isFuture()) {
            return true;
        }

        // Allow access for active subscriptions
        return $tenant->subscription_status === 'active';
    }
}

