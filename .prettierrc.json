{"semi": true, "singleQuote": true, "tabWidth": 2, "printWidth": 100, "trailingComma": "es5", "arrowParens": "always", "bracketSpacing": true, "endOfLine": "lf", "htmlWhitespaceSensitivity": "css", "bracketSameLine": false, "jsxSingleQuote": false, "proseWrap": "preserve", "quoteProps": "as-needed", "plugins": ["prettier-plugin-tailwindcss", "@shufo/prettier-plugin-blade"], "overrides": [{"files": ["*.blade.php"], "options": {"parser": "blade"}}, {"files": ["*.js"], "options": {"parser": "babel"}}]}