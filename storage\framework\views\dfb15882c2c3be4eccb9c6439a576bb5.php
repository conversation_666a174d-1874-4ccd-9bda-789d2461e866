<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['class' => '']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['class' => '']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>
<button
  <?php echo e($attributes->merge(['type' => 'button', 'class' => 'inline-flex w-full justify-center rounded-md bg-red-600 dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-white dark:text-red-400 ring-1 ring-red-600 dark:ring-gray-600 ring-inset shadow-xs hover:bg-red-500 dark:hover:bg-red-500 dark:hover:text-gray-100 sm:ml-3 sm:w-auto ' . $class])); ?>>
  <?php echo e($slot); ?>

</button>
<?php /**PATH C:\laragon\www\mywhats\resources\views/components/button/delete-button.blade.php ENDPATH**/ ?>