@extends('layouts.app')

@section('title', 'Create New Tenant')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Create New Tenant</h1>
            <p class="text-gray-600 mt-2">Set up a new tenant with their subscription plan and owner account</p>
        </div>

        <!-- Form -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <form method="POST" action="{{ route('superadmin.tenants.store') }}" class="space-y-6">
                @csrf

                <!-- Tenant Information -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Tenant Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Tenant Name *</label>
                            <input type="text" id="name" name="name" value="{{ old('name') }}" required
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 @error('name') border-red-500 @enderror">
                            @error('name')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="slug" class="block text-sm font-medium text-gray-700 mb-1">Slug *</label>
                            <input type="text" id="slug" name="slug" value="{{ old('slug') }}" required
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 @error('slug') border-red-500 @enderror">
                            <p class="text-xs text-gray-500 mt-1">Used for subdomain (e.g., slug.yourapp.com)</p>
                            @error('slug')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div class="mt-4">
                        <label for="domain" class="block text-sm font-medium text-gray-700 mb-1">Custom Domain</label>
                        <input type="text" id="domain" name="domain" value="{{ old('domain') }}"
                               placeholder="example.com"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 @error('domain') border-red-500 @enderror">
                        <p class="text-xs text-gray-500 mt-1">Optional custom domain for this tenant</p>
                        @error('domain')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Subscription Plan -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Subscription Plan</h3>
                    
                    <div>
                        <label for="subscription_plan_id" class="block text-sm font-medium text-gray-700 mb-1">Plan *</label>
                        <select id="subscription_plan_id" name="subscription_plan_id" required
                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 @error('subscription_plan_id') border-red-500 @enderror">
                            <option value="">Select a plan</option>
                            @foreach($plans as $plan)
                            <option value="{{ $plan->id }}" {{ old('subscription_plan_id') == $plan->id ? 'selected' : '' }}>
                                {{ $plan->name }} - {{ $plan->formatted_price }}/{{ $plan->billing_cycle }}
                            </option>
                            @endforeach
                        </select>
                        @error('subscription_plan_id')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Owner Information -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Owner Account</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="owner_firstname" class="block text-sm font-medium text-gray-700 mb-1">First Name *</label>
                            <input type="text" id="owner_firstname" name="owner_firstname" value="{{ old('owner_firstname') }}" required
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 @error('owner_firstname') border-red-500 @enderror">
                            @error('owner_firstname')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="owner_lastname" class="block text-sm font-medium text-gray-700 mb-1">Last Name *</label>
                            <input type="text" id="owner_lastname" name="owner_lastname" value="{{ old('owner_lastname') }}" required
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 @error('owner_lastname') border-red-500 @enderror">
                            @error('owner_lastname')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div class="mt-4">
                        <label for="owner_email" class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
                        <input type="email" id="owner_email" name="owner_email" value="{{ old('owner_email') }}" required
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 @error('owner_email') border-red-500 @enderror">
                        <p class="text-xs text-gray-500 mt-1">A random password will be generated and sent to this email</p>
                        @error('owner_email')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <a href="{{ route('superadmin.tenants.index') }}" 
                       class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg font-medium transition duration-200">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition duration-200">
                        Create Tenant
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const slugInput = document.getElementById('slug');
    
    nameInput.addEventListener('input', function() {
        const slug = this.value
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
        slugInput.value = slug;
    });
});
</script>
@endsection

