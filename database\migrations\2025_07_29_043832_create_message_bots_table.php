<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('message_bots', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('keywords'); // Keywords that trigger this bot
            $table->text('response_message');
            $table->string('bot_type')->default('keyword'); // keyword, ai, flow
            $table->boolean('is_active')->default(true);
            $table->integer('trigger_count')->default(0);
            $table->json('conditions')->nullable(); // Advanced conditions
            $table->json('actions')->nullable(); // Actions to perform
            $table->integer('priority')->default(0); // Bot priority order
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            
            $table->index(['is_active']);
            $table->index(['bot_type']);
            $table->index(['priority']);
            $table->index(['created_by']);
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('message_bots');
    }
};
