<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('receiver_id');
            $table->text('last_message')->nullable();
            $table->string('last_msg_time')->nullable();
            $table->string('wa_no')->nullable();
            $table->string('wa_no_id')->nullable();
            $table->string('time_sent');
            $table->string('type')->nullable();
            $table->string('type_id')->nullable();
            $table->string('agent')->nullable();
            $table->boolean('is_ai_chat')->default(0);
            $table->json('ai_message_json')->nullable();
            $table->boolean('is_bots_stoped')->nullable()->default(0);
            $table->string('bot_stoped_time')->nullable();
            $table->timestamps();
            
            $table->index(['receiver_id']);
            $table->index(['wa_no']);
            $table->index(['agent']);
            $table->index(['is_ai_chat']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat');
    }
};
