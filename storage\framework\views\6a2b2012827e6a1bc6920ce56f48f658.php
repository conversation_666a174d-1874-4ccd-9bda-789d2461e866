<div style="color-scheme: dark;">
    <?php
        $user = auth()->user();
    ?>
    <!-- Off-canvas menu for mobile, show/hide based on off-canvas menu state. -->

    <div x-cloak x-show="open" class="relative z-40 lg:hidden" role="dialog" aria-modal="true" x-data="{ mobileOpen: false }">
        <div x-show="open" x-transition:enter="transition-opacity ease-linear duration-300"
            x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
            x-transition:leave="transition-opacity ease-linear duration-300" x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0" x-on:click="open = false"
            class="fixed inset-0 bg-slate-600 bg-opacity-75"></div>

        <div class="fixed inset-0 flex z-40">
            <!-- Mobile Menu (Overlapping Open Menu) -->
            <div x-show="mobileOpen"
                class="absolute top-0 left-0 z-50 lg:hidden w-80 h-full bg-white dark:bg-slate-800 shadow-lg"
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0 transform -translate-x-full"
                x-transition:enter-end="opacity-100 transform translate-x-0"
                x-transition:leave="transition ease-in duration-300"
                x-transition:leave-start="opacity-100 transform translate-x-0"
                x-transition:leave-end="opacity-0 transform -translate-x-full" x-init="mobileOpen = <?php echo e(json_encode(
                    request()->routeIs(
                        'admin.users.*',
                        'admin.roles.*',
                        'admin.status',
                        'admin.source',
                        'admin.ai-prompt',
                        'admin.canned-reply',
                        'admin.activity-log.*',
                        'admin.languages',
                        'admin.emails',
                        'admin.logs.index',
                    ),
                )); ?>">

                <!-- Close Button -->
                <div class="flex justify-between items-center py-4 flex-shrink-0 px-5 bg-white dark:bg-slate-800">
                    <span class="text-lg font-semibold text-gray-600 dark:text-slate-300"> <?php echo e(t('setup')); ?>

                    </span>
                    <!-- Close Button -->
                    <button x-on:click.stop="mobileOpen = false" class="text-gray-500 dark:text-slate-400">
                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-x-mark'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                    </button>
                </div>

                <div class="flex-1 flex flex-col overflow-y-auto">
                    <nav class="flex-1 px-2">

                        <!-- Users -->
                        <!--[if BLOCK]><![endif]--><?php if(checkPermission('user.view')): ?>
                            <a wire:navigate href="<?php echo e(route('admin.users.list')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                    <?php echo e(request()->routeIs('admin.users.list')
                                        ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                        : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-users'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                        '.e(request()->routeIs('admin.users.list')
                                            ? 'text-indigo-600 dark:text-slate-300'
                                            : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('user')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Role -->
                        <!--[if BLOCK]><![endif]--><?php if(checkPermission('role.view') && Auth::user()->is_admin): ?>
                            <a wire:navigate href="<?php echo e(route('admin.roles.list')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                    <?php echo e(request()->routeIs('admin.roles.list')
                                        ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                        : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-swatch'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                        '.e(request()->routeIs('admin.roles.list')
                                            ? 'text-indigo-600 dark:text-slate-300'
                                            : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('role')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!--[if BLOCK]><![endif]--><?php if(checkPermission('status.view')): ?>
                            <a wire:navigate href="<?php echo e(route('admin.status')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                    <?php echo e(request()->routeIs('admin.status')
                                        ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                        : 'text-gray-600 hover:bg-indigo-50 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-c-adjustments-horizontal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                    '.e(request()->routeIs('admin.status')
                                        ? 'text-indigo-600 dark:text-slate-300'
                                        : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('status')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!--[if BLOCK]><![endif]--><?php if(checkPermission('source.view')): ?>
                            <a wire:navigate href="<?php echo e(route('admin.source')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                    <?php echo e(request()->routeIs('admin.source')
                                        ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                        : 'text-gray-600 hover:bg-indigo-50 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-square-3-stack-3d'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                    '.e(request()->routeIs('admin.source')
                                        ? 'text-indigo-600 dark:text-slate-300'
                                        : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('source')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- AI Prompts -->
                        <!--[if BLOCK]><![endif]--><?php if(checkPermission('ai_prompt.view')): ?>
                            <a wire:navigate href="<?php echo e(route('admin.ai-prompt')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                <?php echo e(request()->routeIs('admin.ai-prompt')
                                    ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                    : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-rocket-launch'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                '.e(request()->routeIs('admin.ai-prompt')
                                    ? 'text-indigo-600 dark:text-slate-300'
                                    : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('ai_prompts')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Canned Reply -->
                        <!--[if BLOCK]><![endif]--><?php if(checkPermission('canned_reply.view')): ?>
                            <a wire:navigate href="<?php echo e(route('admin.canned-reply')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                        <?php echo e(request()->routeIs('admin.canned-reply')
                            ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                            : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-m-arrow-right-on-rectangle'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                            '.e(request()->routeIs('admin.canned-reply')
                                ? 'text-indigo-600 dark:text-slate-300'
                                : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('canned_reply')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Activity Log -->
                        <!--[if BLOCK]><![endif]--><?php if(checkPermission('activity_log.view')): ?>
                            <a wire:navigate href="<?php echo e(route('admin.activity-log.list')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                    <?php echo e(request()->routeIs('admin.activity-log.list')
                                        ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                        : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-s-arrow-path'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                        '.e(request()->routeIs('admin.activity-log.list')
                                            ? 'text-indigo-600 dark:text-slate-300'
                                            : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('activity_log')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Language for desktop -->
                        <!--[if BLOCK]><![endif]--><?php if($user->is_admin == 1): ?>
                            <a wire:navigate href="<?php echo e(route('admin.languages')); ?>" class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                                'group flex items-center px-4 py-2 text-sm font-medium rounded-r-md',
                                request()->routeIs('admin.languages')
                                    ? 'border-l-4 border-indigo-600 bg-indigo-50 dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                    : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white',
                            ]); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-s-language'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Illuminate\Support\Arr::toCssClasses([
                                    'mr-4 flex-shrink-0 h-6 w-6',
                                    request()->routeIs('admin.languages')
                                        ? 'text-indigo-600 dark:text-slate-300'
                                        : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300',
                                ])),'aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('languages')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Email Templates -->
                        <!--[if BLOCK]><![endif]--><?php if(checkPermission('email_template.view')): ?>
                            <a wire:navigate href="<?php echo e(route('admin.emails')); ?>" class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                                'group flex items-center px-4 py-2 text-sm font-medium rounded-r-md',
                                request()->routeIs('admin.emails')
                                    ? 'border-l-4 border-indigo-600 bg-indigo-50 dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                    : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white',
                            ]); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-envelope'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Illuminate\Support\Arr::toCssClasses([
                                    'mr-4 flex-shrink-0 h-6 w-6',
                                    request()->routeIs('admin.emails')
                                        ? 'text-indigo-600 dark:text-slate-300'
                                        : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300',
                                ])),'aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('email_template_list_title')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        
                        <!--[if BLOCK]><![endif]--><?php if($user->is_admin == 1): ?>
                            <a wire:navigate href="<?php echo e(route('admin.logs.index')); ?>" class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                                'group flex items-center px-4 py-2 text-sm font-medium rounded-r-md',
                                request()->routeIs('admin.logs.index')
                                    ? 'border-l-4 border-indigo-600 bg-indigo-50 dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                    : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white',
                            ]); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-document-chart-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Illuminate\Support\Arr::toCssClasses([
                                    'mr-4 flex-shrink-0 h-6 w-6',
                                    request()->routeIs('admin.logs.index')
                                        ? 'text-indigo-600 dark:text-slate-300'
                                        : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300',
                                ])),'aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('system_logs')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    </nav>
                </div>
            </div>
            <div x-show="open" x-transition:enter="transition ease-in-out duration-300 transform"
                x-transition:enter-start="-translate-x-full" x-transition:enter-end="translate-x-0"
                x-transition:leave="transition ease-in-out duration-300 transform"
                x-transition:leave-start="translate-x-0" x-transition:leave-end="-translate-x-full"
                x-on:click.away="open = false" class="relative flex flex-col pt-5 bg-white dark:bg-slate-800">
                <div x-show="open" x-transition:enter="ease-in-out duration-300" x-transition:enter-start="opacity-0"
                    x-transition:enter-end="opacity-100" x-transition:leave="ease-in-out duration-300"
                    x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                    class="absolute top-0 right-0 -mr-12 pt-2">
                </div>

                <div class="flex-shrink-0 flex items-center justify-center w-full ">
                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="flex items-center bg-white dark:bg-slate-800">
                        <img x-bind:src="theme === 'light' || (theme === 'system' && window.matchMedia(
                                    '(prefers-color-scheme: light)')
                                .matches) ?
                            '<?php echo e(get_setting('general.site_logo') ? Storage::url(get_setting('general.site_logo')) : url('./img/light_logo.png')); ?>' :
                            '<?php echo e(get_setting('general.site_dark_logo') ? Storage::url(get_setting('general.site_dark_logo')) : url('./img/dark_logo.png')); ?>'"
                            alt="#" class="h-12 md:h-10 sm:h-12 px-4 w-auto object-cover" x-cloak>
                    </a>
                </div>
                <div class="mt-5 flex-1 h-0 overflow-y-auto">
                    <nav class="px-2 py-4">
                        
                        <!--[if BLOCK]><![endif]--><?php if(request()->routeIs('admin.*')): ?>
                            <a wire:navigate href="<?php echo e(route('admin.dashboard')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                            <?php echo e(request()->routeIs('admin.dashboard')
                                ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-squares-2x2'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                            '.e(request()->routeIs('admin.dashboard')
                                ? 'text-indigo-600 dark:text-slate-300'
                                : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('dashboard')); ?>

                            </a>

                            <!--[if BLOCK]><![endif]--><?php if(
                                (get_setting('whatsapp.is_whatsmark_connected') == 0 || get_setting('whatsapp.is_webhook_connected') == 0) &&
                                    checkPermission('connect_account.connect')): ?>
                                <a wire:navigate href="<?php echo e(route('admin.connect')); ?>"
                                    class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                            <?php echo e(request()->routeIs('admin.connect')
                                ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                            '.e(request()->routeIs('admin.connect')
                                ? 'text-indigo-600 dark:text-slate-300'
                                : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('connect_waba')); ?>

                                </a>
                            <?php elseif(get_setting('whatsapp.is_whatsmark_connected') == 1 &&
                                    get_setting('whatsapp.is_webhook_connected') == 1 &&
                                    checkPermission('connect_account.view')): ?>
                                <a wire:navigate href="<?php echo e(route('admin.waba')); ?>"
                                    class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                            <?php echo e(request()->routeIs('admin.waba')
                                ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                            '.e(request()->routeIs('admin.waba')
                                ? 'text-indigo-600 dark:text-slate-300'
                                : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('connect_waba')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!-- Menu Items -->
                            <!--[if BLOCK]><![endif]--><?php if(checkPermission('contact.view')): ?>
                                <p class="text-sm text-gray-500 dark:text-slate-400 font-meduim px-5 py-4">
                                    <?php echo e(t('contact')); ?>

                                </p>

                                <a wire:navigate href="<?php echo e(route('admin.contacts.list')); ?>"
                                    class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                            <?php echo e(request()->routeIs('admin.contacts.list')
                                                ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                                : 'text-gray-600 hover:bg-indigo-50 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-user-circle'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                            '.e(request()->routeIs('admin.contacts.list')
                                                ? 'text-indigo-700 dark:text-slate-300'
                                                : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('contact')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!--[if BLOCK]><![endif]--><?php if(checkPermission('template.view')): ?>
                                <p class="text-sm text-gray-500 dark:text-slate-400 font-meduim px-5 py-4">
                                    <?php echo e(t('templates')); ?>

                                </p>
                                <!-- Menu Items -->
                                <a wire:navigate href="<?php echo e(route('admin.template.list')); ?>"
                                    class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                            <?php echo e(request()->routeIs('admin.template.list')
                                                ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                                : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-document'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                            '.e(request()->routeIs('admin.template.list')
                                                ? 'text-indigo-600 dark:text-slate-300'
                                                : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('templates')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!--[if BLOCK]><![endif]--><?php if(checkPermission(['campaigns.view', 'bulk_campaigns.send', 'message_bot.view', 'template_bot.view'])): ?>
                                <p class="text-sm text-gray-500 dark:text-slate-400 font-meduim px-5 py-4">
                                    <?php echo e(t('marketing')); ?>

                                </p>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            
                            <!--[if BLOCK]><![endif]--><?php if(checkPermission('campaigns.view')): ?>
                                <a wire:navigate href="<?php echo e(route('admin.campaigns.list')); ?>"
                                    class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                        <?php echo e(request()->routeIs('admin.campaigns.list')
                                            ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                            : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-megaphone'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                            '.e(request()->routeIs('admin.campaigns.list')
                                                ? 'text-indigo-600 dark:text-slate-300'
                                                : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('campaign')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            
                            <!--[if BLOCK]><![endif]--><?php if(checkPermission('bulk_campaigns.send')): ?>
                                <a wire:navigate href="<?php echo e(route('admin.csvcampaign')); ?>"
                                    class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                        <?php echo e(request()->routeIs('admin.csvcampaign')
                                            ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                            : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-clipboard-document'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                            '.e(request()->routeIs('admin.csvcampaign')
                                                ? 'text-indigo-600 dark:text-slate-300'
                                                : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('bulk_campaign')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!-- Message Bot -->
                            <!--[if BLOCK]><![endif]--><?php if(checkPermission('message_bot.view')): ?>
                                <a wire:navigate href="<?php echo e(route('admin.messagebot.list')); ?>"
                                    class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                        <?php echo e(request()->routeIs('admin.messagebot.list')
                                            ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                            : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-chat-bubble-bottom-center-text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                            '.e(request()->routeIs('admin.messagebot.list')
                                                ? 'text-indigo-600 dark:text-slate-300'
                                                : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('message_bot')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!-- Template Bot -->
                            <!--[if BLOCK]><![endif]--><?php if(checkPermission('template_bot.view')): ?>
                                <a wire:navigate href="<?php echo e(route('admin.templatebot.list')); ?>"
                                    class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                    <?php echo e(request()->routeIs('admin.templatebot.list')
                                        ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                        : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-tag'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                            '.e(request()->routeIs('admin.templatebot.list')
                                                ? 'text-indigo-600 dark:text-slate-300'
                                                : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('template_bot')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!-- Chat -->
                            <!--[if BLOCK]><![endif]--><?php if(checkPermission('chat.view')): ?>
                                <p class="text-sm text-gray-500 dark:text-slate-400 font-meduim px-5 py-4">
                                    <?php echo e(t('support')); ?>

                                </p>
                                <a wire:navigate href="<?php echo e(route('admin.chat')); ?>"
                                    class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                            <?php echo e(request()->routeIs('admin.chat')
                                ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-chat-bubble-oval-left'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                '.e(request()->routeIs('admin.chat')
                                    ? 'text-indigo-600 dark:text-slate-300'
                                    : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('chat')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <!-- Settings -->
                            <!--[if BLOCK]><![endif]--><?php if(checkPermission(['system_settings.view', 'system_settings.edit', 'whatsmark_settings.view'])): ?>
                                <p class="text-sm text-gray-500 dark:text-slate-400 font-meduim px-5 py-4">
                                    <?php echo e(t('settings')); ?>

                                </p>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <!--[if BLOCK]><![endif]--><?php if(checkPermission('system_settings.view')): ?>
                                <a wire:navigate href="<?php echo e(route('admin.general.settings.view')); ?>"
                                    class="group flex items-center px-5 py-2 text-sm font-medium rounded-r-md
                                    <?php echo e(in_array(request()->route()->getName(), [
                                        'admin.general.settings.view',
                                        'admin.email.settings.view',
                                        'admin.re-captcha.settings.view',
                                        'admin.announcement.settings.view',
                                        'admin.cron-job.settings.view',
                                        'admin.seo.settings.view',
                                        'admin.pusher.settings.view',
                                        'admin.system-update.settings.view',
                                        'admin.system-information.settings.view',
                                        'admin.notification.settings.view',
                                    ])
                                        ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                        : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-cog'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-2 flex-shrink-0 h-6 w-6
                                    '.e(in_array(request()->route()->getName(), [
                                        'admin.general.settings.view',
                                        'admin.email.settings.view',
                                        'admin.re-captcha.settings.view',
                                        'admin.announcement.settings.view',
                                        'admin.cron-job.settings.view',
                                        'admin.seo.settings.view',
                                        'admin.pusher.settings.view',
                                        'admin.system-update.settings.view',
                                        'admin.system-information.settings.view',
                                        'admin.notification.settings.view',
                                    ])
                                        ? 'text-indigo-600 dark:text-slate-300'
                                        : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('system_setting')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!-- WhatsMark Settings -->
                            <!--[if BLOCK]><![endif]--><?php if(checkPermission('whatsmark_settings.view')): ?>
                                <a wire:navigate href="<?php echo e(route('admin.whatsapp-auto-lead.settings.view')); ?>"
                                    class="group flex items-center px-5 py-2 text-sm font-medium rounded-r-md
                            <?php echo e(in_array(request()->route()->getName(), [
                                'admin.whatsapp-auto-lead.settings.view',
                                'admin.stop-bot.settings.view',
                                'admin.web-hooks.settings.view',
                                'admin.support-agent.settings.view',
                                'admin.notification-sound.settings.view',
                                'admin.ai-integration.settings.view',
                                'admin.auto-clear-chat-history.settings.view',
                            ])
                                ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-wrench-screwdriver'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-2 flex-shrink-0 h-6 w-6
                            '.e(in_array(request()->route()->getName(), [
                                'admin.whatsapp-auto-lead.settings.view',
                                'admin.stop-bot.settings.view',
                                'admin.web-hooks.settings.view',
                                'admin.support-agent.settings.view',
                                'admin.notification-sound.settings.view',
                                'admin.ai-integration.settings.view',
                                'admin.auto-clear-chat-history.settings.view',
                            ])
                                ? 'text-indigo-600 dark:text-slate-300'
                                : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('whatsmark_settings')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <!-- Setup -->
                            <!--[if BLOCK]><![endif]--><?php if(checkPermission([
                                    'user.view',
                                    'role.view',
                                    'status.view',
                                    'source.view',
                                    'ai_prompt.view',
                                    'canned_reply.view',
                                    'activity_log.view',
                                    'email_template.view',
                                ])): ?>
                                <button x-on:click.prevent="mobileOpen = true"
                                    class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white mt-2 w-full">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-cog-6-tooth'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6 text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('setup')); ?>

                                </button>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        
                    </nav>
                </div>
            </div>

            <div class="flex-shrink-0 w-14" aria-hidden="true">
                <!-- Dummy element to force sidebar to shrink to fit close icon -->
            </div>
        </div>
    </div>
    <!-- Static sidebar for desktop -->
    <div class="hidden lg:flex lg:w-[15rem] lg:fixed lg:inset-y-0 z-40" x-data="{ setupMenu: <?php echo e(request()->routeIs('admin.users.list', 'admin.roles.list', 'admin.status', 'admin.source', 'admin.ai-prompt', 'admin.canned-reply', 'admin.activity-log.list', 'admin.languages', 'admin.emails', 'admin.logs.index') ? 'true' : 'false'); ?> }">
        <div class="flex-1 flex flex-col min-h-0 border-r border-slate-300 dark:border-r dark:border-slate-600">
            <!-- Close Button -->

            <div x-show="setupMenu" x-cloak class="hidden lg:flex lg:w-[15rem] lg:fixed lg:inset-y-0"
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0 transform -translate-x-full"
                x-transition:enter-end="opacity-100 transform translate-x-0"
                x-transition:leave="transition ease-in duration-300"
                x-transition:leave-start="opacity-100 transform translate-x-0"
                x-transition:leave-end="opacity-0 transform -translate-x-full">
                <div class="flex-1 flex flex-col min-h-0 border-r border-slate-300 dark:border-slate-600">
                    <!-- Top bar with Close button -->
                    <div class="flex justify-between items-center py-4 flex-shrink-0 px-5 bg-white dark:bg-slate-800">
                        <span class="text-lg font-semibold text-gray-600 dark:text-slate-300">
                            <?php echo e(t('setup')); ?>

                        </span>
                        <!-- Close Button -->
                        <button x-on:click="setupMenu = false" class="text-gray-500 dark:text-slate-400">
                            <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-x-mark'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                        </button>
                    </div>

                    <div class="flex-1 flex flex-col overflow-y-auto bg-white dark:bg-slate-800">
                        <nav class="flex-1 px-2">
                            <!-- Users -->
                            <!--[if BLOCK]><![endif]--><?php if(checkPermission('user.view')): ?>
                                <a wire:navigate href="<?php echo e(route('admin.users.list')); ?>"
                                    class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                        <?php echo e(request()->routeIs('admin.users.list')
                                            ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                            : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-users'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                            '.e(request()->routeIs('admin.users.list')
                                                ? 'text-indigo-600 dark:text-slate-300'
                                                : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('user')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!-- Role -->
                            <!--[if BLOCK]><![endif]--><?php if(checkPermission('role.view') && Auth::user()->is_admin): ?>
                                <a wire:navigate href="<?php echo e(route('admin.roles.list')); ?>"
                                    class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                        <?php echo e(request()->routeIs('admin.roles.list')
                                            ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                            : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-swatch'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                            '.e(request()->routeIs('admin.roles.list')
                                                ? 'text-indigo-600 dark:text-slate-300'
                                                : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('role')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!--[if BLOCK]><![endif]--><?php if(checkPermission('status.view')): ?>
                                <a wire:navigate href="<?php echo e(route('admin.status')); ?>"
                                    class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                        <?php echo e(request()->routeIs('admin.status')
                                            ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                            : 'text-gray-600 hover:bg-indigo-50 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-c-adjustments-horizontal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                        '.e(request()->routeIs('admin.status')
                                            ? 'text-indigo-600 dark:text-slate-300'
                                            : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('status')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!--[if BLOCK]><![endif]--><?php if(checkPermission('source.view')): ?>
                                <a wire:navigate href="<?php echo e(route('admin.source')); ?>"
                                    class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                    <?php echo e(request()->routeIs('admin.source')
                                        ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                        : 'text-gray-600 hover:bg-indigo-50 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-square-3-stack-3d'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                    '.e(request()->routeIs('admin.source')
                                        ? 'text-indigo-600 dark:text-slate-300'
                                        : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('source')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!-- AI Prompts -->

                            <!--[if BLOCK]><![endif]--><?php if(checkPermission('ai_prompt.view')): ?>
                                <a wire:navigate href="<?php echo e(route('admin.ai-prompt')); ?>"
                                    class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                    <?php echo e(request()->routeIs('admin.ai-prompt')
                                        ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                        : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-rocket-launch'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                    '.e(request()->routeIs('admin.ai-prompt')
                                        ? 'text-indigo-600 dark:text-slate-300'
                                        : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('ai_prompts')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!-- Canned Reply -->
                            <!--[if BLOCK]><![endif]--><?php if(checkPermission('canned_reply.view')): ?>
                                <a wire:navigate href="<?php echo e(route('admin.canned-reply')); ?>"
                                    class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                    <?php echo e(request()->routeIs('admin.canned-reply')
                                        ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                        : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-m-arrow-right-on-rectangle'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                    '.e(request()->routeIs('admin.canned-reply')
                                        ? 'text-indigo-600 dark:text-slate-300'
                                        : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('canned_reply')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <!-- Activity Log -->
                            <!--[if BLOCK]><![endif]--><?php if(checkPermission('activity_log.view')): ?>
                                <a wire:navigate href="<?php echo e(route('admin.activity-log.list')); ?>"
                                    class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                    <?php echo e(request()->routeIs('admin.activity-log.list')
                                        ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                        : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-s-arrow-path'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                        '.e(request()->routeIs('admin.activity-log.list')
                                            ? 'text-indigo-600 dark:text-slate-300'
                                            : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('activity_log')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!-- Language for desktop -->
                            <!--[if BLOCK]><![endif]--><?php if($user->is_admin == 1): ?>
                                <a wire:navigate href="<?php echo e(route('admin.languages')); ?>" class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                                    'group flex items-center px-4 py-2 text-sm font-medium rounded-r-md',
                                    request()->routeIs('admin.languages')
                                        ? 'border-l-4 border-indigo-600 bg-indigo-50 dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                        : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white',
                                ]); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-s-language'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Illuminate\Support\Arr::toCssClasses([
                                        'mr-4 flex-shrink-0 h-6 w-6',
                                        request()->routeIs('admin.languages')
                                            ? 'text-indigo-600 dark:text-slate-300'
                                            : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300',
                                    ])),'aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('languages')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!-- Email Templates -->
                            <!--[if BLOCK]><![endif]--><?php if(checkPermission('email_template.view')): ?>
                                <a wire:navigate href="<?php echo e(route('admin.emails')); ?>" class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                                    'group flex items-center px-4 py-2 text-sm font-medium rounded-r-md',
                                    request()->routeIs('admin.emails')
                                        ? 'border-l-4 border-indigo-600 bg-indigo-50 dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                        : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white',
                                ]); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-envelope'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Illuminate\Support\Arr::toCssClasses([
                                        'mr-4 flex-shrink-0 h-6 w-6',
                                        request()->routeIs('admin.emails')
                                            ? 'text-indigo-600 dark:text-slate-300'
                                            : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300',
                                    ])),'aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('email_template_list_title')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            
                            <!--[if BLOCK]><![endif]--><?php if($user->is_admin == 1): ?>
                                <a wire:navigate href="<?php echo e(route('admin.logs.index')); ?>"
                                    class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                                        'group flex items-center px-4 py-2 text-sm font-medium rounded-r-md',
                                        request()->routeIs('admin.logs.index')
                                            ? 'border-l-4 border-indigo-600 bg-indigo-50 dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                            : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white',
                                    ]); ?>">
                                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-document-chart-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(\Illuminate\Support\Arr::toCssClasses([
                                        'mr-4 flex-shrink-0 h-6 w-6',
                                        request()->routeIs('admin.logs.index')
                                            ? 'text-indigo-600 dark:text-slate-300'
                                            : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300',
                                    ])),'aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php echo e(t('system_logs')); ?>

                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        </nav>
                    </div>
                </div>
            </div>
            <div class="flex justify-center">
                <a wire:navigate href="<?php echo e(route('admin.dashboard')); ?>"
                    class="flex items-center bg-white dark:bg-slate-800">
                    <img x-bind:src="theme === 'light' || (theme === 'system' && window.matchMedia(
                                '(prefers-color-scheme: light)')
                            .matches) ?
                        '<?php echo e(get_setting('general.site_logo') ? Storage::url(get_setting('general.site_logo')) : url('./img/light_logo.png')); ?>' :
                        '<?php echo e(get_setting('general.site_dark_logo') ? Storage::url(get_setting('general.site_dark_logo')) : url('./img/dark_logo.png')); ?>'"
                        alt="#" class="h-20 my-1 object-contain" x-cloak>
                </a>
            </div>

            <div class="flex-1 flex flex-col overflow-y-auto bg-white dark:bg-slate-800">
                <nav class="flex-1 px-2 py-4">
                    
                    <!--[if BLOCK]><![endif]--><?php if(request()->routeIs('admin.*')): ?>
                        <a wire:navigate href="<?php echo e(route('admin.dashboard')); ?>"
                            class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                        <?php echo e(request()->routeIs('admin.dashboard')
                            ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                            : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                            <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-squares-2x2'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                        '.e(request()->routeIs('admin.dashboard')
                            ? 'text-indigo-600 dark:text-slate-300'
                            : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                            <?php echo e(t('dashboard')); ?>

                        </a>

                        <!--[if BLOCK]><![endif]--><?php if(
                            (get_setting('whatsapp.is_whatsmark_connected') == 0 || get_setting('whatsapp.is_webhook_connected') == 0) &&
                                checkPermission('connect_account.connect')): ?>
                            <a wire:navigate href="<?php echo e(route('admin.connect')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                            <?php echo e(request()->routeIs('admin.connect')
                                ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                            '.e(request()->routeIs('admin.connect')
                                ? 'text-indigo-600 dark:text-slate-300'
                                : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('connect_waba')); ?>

                            </a>
                        <?php elseif(get_setting('whatsapp.is_whatsmark_connected') == 1 &&
                                get_setting('whatsapp.is_webhook_connected') == 1 &&
                                checkPermission('connect_account.view')): ?>
                            <a wire:navigate href="<?php echo e(route('admin.waba')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                            <?php echo e(request()->routeIs('admin.waba')
                                ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                            '.e(request()->routeIs('admin.waba')
                                ? 'text-indigo-600 dark:text-slate-300'
                                : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('connect_waba')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Menu Items -->
                        <!--[if BLOCK]><![endif]--><?php if(checkPermission('contact.view')): ?>
                            <p class="text-sm text-gray-500 dark:text-slate-400 font-meduim px-5 py-4">
                                <?php echo e(t('contact')); ?>

                            </p>
                            <a wire:navigate href="<?php echo e(route('admin.contacts.list')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                        <?php echo e(request()->routeIs('admin.contacts.list')
                                            ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                            : 'text-gray-600 hover:bg-indigo-50 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-user-circle'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                        '.e(request()->routeIs('admin.contacts.list')
                                            ? 'text-indigo-700 dark:text-slate-300'
                                            : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('contact')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!--[if BLOCK]><![endif]--><?php if(checkPermission('template.view')): ?>
                            <p class="text-sm text-gray-500 dark:text-slate-400 font-meduim px-5 py-4">
                                <?php echo e(t('templates')); ?>

                            </p>
                            <!-- Menu Items -->
                            <a wire:navigate href="<?php echo e(route('admin.template.list')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                        <?php echo e(request()->routeIs('admin.template.list')
                            ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                            : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-document'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                            '.e(request()->routeIs('admin.template.list')
                                ? 'text-indigo-600 dark:text-slate-300'
                                : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('templates')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!--[if BLOCK]><![endif]--><?php if(checkPermission([
                                'campaigns.view',
                                'campaigns.show_campaign',
                                'bulk_campaigns.send',
                                'message_bot.view',
                                'template_bot.view',
                            ])): ?>
                            <p class="text-sm text-gray-500 dark:text-slate-400 font-meduim px-5 py-4">
                                <?php echo e(t('marketing')); ?>

                            </p>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!--[if BLOCK]><![endif]--><?php if(checkPermission('campaigns.view')): ?>
                            <a wire:navigate href="<?php echo e(route('admin.campaigns.list')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                    <?php echo e(request()->routeIs('admin.campaigns.list')
                                        ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                        : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-megaphone'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                        '.e(request()->routeIs('admin.campaigns.list')
                                            ? 'text-indigo-600 dark:text-slate-300'
                                            : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('campaign')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        
                        <!--[if BLOCK]><![endif]--><?php if(checkPermission('bulk_campaigns.send')): ?>
                            <a wire:navigate href="<?php echo e(route('admin.csvcampaign')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                 <?php echo e(request()->routeIs('admin.csvcampaign')
                                     ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                     : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-clipboard-document'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                     '.e(request()->routeIs('admin.csvcampaign')
                                         ? 'text-indigo-600 dark:text-slate-300'
                                         : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('bulk_campaign')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <!-- Message Bot -->
                        <!--[if BLOCK]><![endif]--><?php if(checkPermission('message_bot.view')): ?>
                            <a wire:navigate href="<?php echo e(route('admin.messagebot.list')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                                <?php echo e(request()->routeIs('admin.messagebot.list')
                                    ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                    : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-chat-bubble-bottom-center-text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                    '.e(request()->routeIs('admin.messagebot.list')
                                        ? 'text-indigo-600 dark:text-slate-300'
                                        : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('message_bot')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Template Bot -->
                        <!--[if BLOCK]><![endif]--><?php if(checkPermission('template_bot.view')): ?>
                            <a wire:navigate href="<?php echo e(route('admin.templatebot.list')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                            <?php echo e(request()->routeIs('admin.templatebot.list')
                                ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                                : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-tag'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                                    '.e(request()->routeIs('admin.templatebot.list')
                                        ? 'text-indigo-600 dark:text-slate-300'
                                        : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('template_bot')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Chat -->
                        <!--[if BLOCK]><![endif]--><?php if(checkPermission(['chat.view', 'chat.read_only'])): ?>
                            <p class="text-sm text-gray-500 dark:text-slate-400 font-meduim px-5 py-4">
                                <?php echo e(t('support')); ?>

                            </p>

                            <a wire:navigate href="<?php echo e(route('admin.chat')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                        <?php echo e(request()->routeIs('admin.chat')
                            ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                            : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-chat-bubble-oval-left'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                            '.e(request()->routeIs('admin.chat')
                                ? 'text-indigo-600 dark:text-slate-300'
                                : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('chat')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Settings -->
                        <!--[if BLOCK]><![endif]--><?php if(checkPermission(['system_settings.view', 'whatsmark_settings.view'])): ?>
                            <p class="text-sm text-gray-500 dark:text-slate-400 font-meduim px-5 py-4">
                                <?php echo e(t('settings')); ?>

                            </p>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!--[if BLOCK]><![endif]--><?php if(checkPermission('system_settings.view')): ?>
                            <a wire:navigate href="<?php echo e(route('admin.general.settings.view')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                <?php echo e(in_array(request()->route()->getName(), [
                    'admin.general.settings.view',
                    'admin.email.settings.view',
                    'admin.re-captcha.settings.view',
                    'admin.announcement.settings.view',
                    'admin.cron-job.settings.view',
                    'admin.seo.settings.view',
                    'admin.pusher.settings.view',
                    'admin.system-update.settings.view',
                    'admin.system-information.settings.view',
                    'admin.notification.settings.view',
                ])
                    ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                    : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-cog'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                    '.e(in_array(request()->route()->getName(), [
                        'admin.general.settings.view',
                        'admin.email.settings.view',
                        'admin.re-captcha.settings.view',
                        'admin.announcement.settings.view',
                        'admin.cron-job.settings.view',
                        'admin.seo.settings.view',
                        'admin.pusher.settings.view',
                        'admin.system-update.settings.view',
                        'admin.system-information.settings.view',
                        'admin.notification.settings.view',
                    ])
                        ? 'text-indigo-600 dark:text-slate-300'
                        : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('system_setting')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- WhatsMark Settings -->
                        <!--[if BLOCK]><![endif]--><?php if(checkPermission('whatsmark_settings.view')): ?>
                            <a wire:navigate href="<?php echo e(route('admin.whatsapp-auto-lead.settings.view')); ?>"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md
                <?php echo e(in_array(request()->route()->getName(), [
                    'admin.whatsapp-auto-lead.settings.view',
                    'admin.stop-bot.settings.view',
                    'admin.web-hooks.settings.view',
                    'admin.support-agent.settings.view',
                    'admin.notification-sound.settings.view',
                    'admin.ai-integration.settings.view',
                    'admin.auto-clear-chat-history.settings.view',
                ])
                    ? 'border-l-4 border-indigo-600 bg-indigo-50  dark:border-indigo-600 text-indigo-700 dark:bg-slate-900 dark:text-white'
                    : 'text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white'); ?>">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-wrench-screwdriver'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6
                    '.e(in_array(request()->route()->getName(), [
                        'admin.whatsapp-auto-lead.settings.view',
                        'admin.stop-bot.settings.view',
                        'admin.web-hooks.settings.view',
                        'admin.support-agent.settings.view',
                        'admin.notification-sound.settings.view',
                        'admin.ai-integration.settings.view',
                        'admin.auto-clear-chat-history.settings.view',
                    ])
                        ? 'text-indigo-600 dark:text-slate-300'
                        : 'text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300').'','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('whatsmark_settings')); ?>

                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Setup -->
                        <!--[if BLOCK]><![endif]--><?php if(checkPermission([
                                'user.view',
                                'role.view',
                                'status.view',
                                'source.view',
                                'ai_prompt.view',
                                'canned_reply.view',
                                'activity_log.view',
                                'email_template.view',
                            ])): ?>
                            <button x-on:click.prevent="setupMenu = true"
                                class="group flex items-center px-4 py-2 text-sm font-medium rounded-r-md text-gray-600 hover:bg-indigo-100 hover:text-indigo-800 dark:text-slate-300 dark:hover:bg-slate-700 dark:hover:text-white mt-2 w-full">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-cog-6-tooth'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mr-4 flex-shrink-0 h-6 w-6 text-gray-500 group-hover:text-indigo-700 dark:text-slate-400 group-hover:dark:text-slate-300','aria-hidden' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                <?php echo e(t('setup')); ?>

                            </button>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    
                </nav>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\laragon\www\mywhats\resources\views/livewire/backend/sidebar-navigation.blade.php ENDPATH**/ ?>