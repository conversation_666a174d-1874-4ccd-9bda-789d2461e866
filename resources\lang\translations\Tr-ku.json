{"welcome_message": "بەخێربێیت بۆ بەرهەمەکانمان", "login": "چوونەژوورەوە", "register": "خۆتۆمارکردن", "email_address": "ناونیشانی ئیمەیڵ", "password": "وشەی نهێنی", "remember_me": "بیربکەوە لە من", "dashboard": "داشبۆرد", "profile_settings": "ڕێکخستنەکانی پرۆفایل", "logout": "چوونە دەرەوە", "features": "Features", "capabilities": "Capabilities", "role_name": "Role Name", "add_role": "Add Role", "create_role": "Create Role", "role_permission_warning": "Changing role permissions won't affect current staff members' permissions that are using this role.", "update_staff_permissions": "Update all staff members' permissions that are using this role", "status": "Status", "new_status": "New Status", "name": "Name", "status_color": "Color", "status_color_placeholder": "#Select color", "delete_status_title": "Delete Status", "source": "Source", "new_source": "New Source", "delete_source_title": "Delete Source", "source_in_use_notify": "The Source ID is already being used", "contact": "Contact", "new_contact_button": "New Contact", "contact_details": "Contact Details", "other_details": "Other Details", "firstname": "First Name", "lastname": "Last Name", "company": "Company", "type": "Type", "type_lead": "Lead", "type_customer": "Customer", "email": "Email", "website": "Website", "default_language": "Default Language", "english": "English", "assigned": "Assigned", "assigned_select": "Select Assigned", "city": "City", "state": "State", "country": "Country", "country_select": "Select Country", "zip_code": "Zip Code", "address": "Address", "description": "Description", "delete_contact_title": "Delete Contact", "ai_prompts": "AI Prompts", "action": "Action", "delete_ai_prompts_title": "Delete AI Prompt", "canned_reply": "Canned Reply", "title": "Title", "delete_canned_title": "Delete Canned Reply", "delete_chat_title": "Delete Chat?", "update_button": "Update", "add_button": "Add", "add_contact_title": "Add New Contact", "edit_contact_title": "Edit Contact", "add_notes_title": "Add Notes", "notes_title": "Notes", "confirm": "Confirm", "forgot_password_fp": "Forgot password", "auth.forgot_password": "Forgot your password? No problem. Just let us know your email address and we will email you a password reset link.", "email_password_fp": "Email Password Reset Link", "login_lb": "Log in", "confirm_password": "Confirm password", "auth.already_registered": "Already registered?", "reset_password_rp": "Reset Password", "varify_email": "Resend Verification Email", "logout_ve": "Log Out", "delete_activity_log_title": "Delete Activity Log", "connect_waba": "Connect WABA", "whatsapp_business_account": "WhatsApp Business Account", "connect_with_facebook_step1": "Step - 1 : Facebook Developer Account & Facebook App", "fb_app_id": "Facebook App ID ", "help": "help", "fb_app_secret": "Facebook App Secret", "webhook": "Connect Webhook", "wp_integration_step2": "Step - 2 : WhatsApp Integration Setup", "wp_business_id": "Your WhatsApp Business Account (WABA) ID", "user_access_token_info": "Your User Access Token after signing up at for an account at Facebook Developers Portal", "wp_access_token": "Whatsapp Access Token", "debug_token": "Debug Token", "config": "Configure", "waba": "WABA", "qr_code": "Click to get QR Code", "disconnect_account": "Disconnect Account", "access_token_info": "Access Token Information", "access_token": "Access token", "permission_scopes": "Permission scopes", "issued": "Issued at", "expiry": "Expiry at", "webhook_url": "Webhook URL", "test_message": "Send Test Message", "wp_message": "Add `+` and the country code before the number (e.g., `+***********`) to send a WhatsApp message.", "wp_number": "WhatsApp number", "send_message": "Send Message", "verify_webhook": "Verify Webhook", "display_phone_number": "Display Phone Number", "verified_name": "Verified Name", "number_id": "Number ID", "quality": "Quality", "manage_phone_numbers": "Manage Phone Numbers", "mark_as_default": "<PERSON> as <PERSON><PERSON><PERSON>", "overall_health": "Overall Health", "whatsapp_business_id": "WhatsApp Business ID", "status_as_at": "Status as at", "overall_health_send_message": "Overall Health of Send Messages", "can_send_message": "Can Send Messages", "refresh_health_status": "Refresh health status", "qr_code_to_start_chat": "Scan QR Code to Start Chat", "qr_code_to_invite_people": "You can use the following QR Codes to invite people on this platform.", "url_for_qr_image": "URL for QR Image", "whatsapp_url": "WhatsApp URL", "whatsapp_now": "WhatsApp Now", "close": "Close", "delete_message": "Are you sure you want to perform this action ?", "open_sidebar": "Open sidebar", "settings": "Settings", "system": "System", "whatsmark": "WhatsMark", "light": "Light", "dark": "Dark", "open_user_menu": "Open user menu", "avatar": "Avatar", "account_profile": "Account Profile", "profile": "Profile", "add_message_bot": "Create Message bot", "edit_bot": "Edit Message bot", "bot_name": "Bot Name", "relation_type": "Relation Type", "data_tippy_content": "Text that will be sent to the lead or contact. You can also use {companyname}, {crm_url} or any other custom merge fields of lead or contact , or use the  sign to find available merge fields", "reply_text": "Reply text (Maximum allowed characters should be 1024)", "reply_type": "Reply Type", "trigger_keyword": "Trigger Keyword", "max_allowed_character_60": "Maximum allowed characters should be 60", "header": "Header", "footer": "Footer", "reply_button_option1": "Option 1: <PERSON><PERSON> with reply <PERSON><PERSON>", "max_allowed_char_20": "Maximum allowed characters should be 20", "button1": "Button1", "max_allow_char_256": "Maximum allowed characters should be 256", "button1_id": "Button1 ID", "button2": "Button2", "button2_id": "Button2 ID", "button3": "Button3", "button3_id": "Button3 ID", "option2_button_name": "Option 2: <PERSON><PERSON> with button link - Call to Action (CTA) URL", "button_name": "Button Name", "button_link": "Button Link", "bot_file_option3": "Option 3: <PERSON><PERSON> with file", "choose_file_type": "Choose File Type", "image": "Image", "document": "Document", "video": "Video", "preview": "Preview", "submit": "Submit", "message_bots": "Message Bots", "delete_message_bot": "Delete Message Bot", "cancel": "Cancel", "delete": "Delete", "set_background_color": "Set background color", "set_link_text_color": "Set link text color", "set_link_message_color": "Set message color", "message_bot_options": "Options", "email_template_list_title": "Email Templates", "email_template_title": "<PERSON>ail Te<PERSON>late", "email_template_name": "Template Name", "email_template_subject": "Subject", "save_changes_button": "Save Changes", "merge_field_name_title": "Field Name", "merge_tag_title": "<PERSON><PERSON>", "merge_not_available": "No merge fields available.", "bulk_campaign": "Bulk Campaign", "campaign_for_csv_file": "Campaigns from CSV File", "campaign": "Campaign", "campaign_name": "Campaign Name", "choose_csv_file": "Choose CSV File", "csv_sample_file_download": "Download Sample File & Read Rules", "download_sample": "Download Sample", "phone_requirement_column": "1. Phone Number Column Requirement:", "phone_req_description": "Your CSV file must include a column named <PERSON><PERSON>. Each record in this column should contain a valid contact number, correctly formatted with the country code, including the '+' sign.", "csv_encoding_format": "2. CSV Format and Encoding: ", "csv_encoding_description": "Your CSV data should follow the specified format. The first row of your CSV file must contain the column headers, as shown in the example table. Ensure that your file is encoded in UTF-8 to prevent any encoding issues.", "<EMAIL>": "<EMAIL>", "upload": "Upload", "system_setting": "System Settings", "email_notification": "Notification Preferences", "email_notification_description": "Configure automated email notifications for various system events, ensuring users stay informed about critical updates.", "msg_for_admins": "When new lead is created, send email notification to all admins", "msg_for_assignees": "When new lead is created, send email notification to all assignees", "general_settings": "General Settings", "system_core_settings": "System Core Settings", "system_core_settings_description": "Configure fundamental system-wide parameters and default behaviors that impact overall application performance and user experience. ", "site_name": "Site Name", "site_description": "Site Description", "localization": "Localization", "timezone": "Timezone", "date_format": "Date Format", "time_format": "Time Format", "logo_favicon": "Logo & Favicon", "site_logo": "Site Logo", "recommended": "Recommended", "img_preview": "Image Preview", "select_or_browse_to": "Select or browse to ", "from_your_gallery": "from your gallery.", "remove_img": "Remove Image", "dark_logo": "Dark Logo", "favicon": "Favicon", "favicon_icon": "Favicon Icon", "24_hours": "24 Hours", "12_hours": "12 Hours", "email_settings": "Email Configuration", "email_settings_description": "Manage SMTP settings, email templates, and outgoing communication preferences to ensure reliable and professional email communications.", "important_information": "Important Information", "imp_info_description": " Please configure your mail server settings accurately. This application will rely on your specified mail server to handle email delivery. Errors encountered during email operations are typically due to incorrect server settings. Ensure all credentials, such as the port, encryption method, and SMTP details, are correct. Use the Send Test Email button to validate your configuration. If an error occurs, review your settings and try again.", "select_smtp_protocol": "Select SMTP Protocol", "smtp_protocol": "SMTP Protocol", "smtp": "SMTP", "smtp_host": "SMTP Host", "smtp_port": "SMTP Port", "smtp_username": "SMTP Username", "smtp_password": "SMTP Password", "smtp_encryption": "SMTP Encryption", "ssl": "SSL", "tls": "TLS", "sender_name": "Sender Name", "your_company_name": "Your Company Name", "sender_email": "Sender <PERSON><PERSON>", "test": "Test", "saving": "Saving...", "sending": "Sending...", "re_captcha": "Re-<PERSON><PERSON>", "bot_protection_description": "Implement advanced bot prevention and user verification mechanisms to protect your application from automated attacks and spam.", "toggle_switch": "Toggle Switch", "enable_recaptcha": "Enable Re-Captcha?", "recaptcha_site_key": "Re-Captcha site key", "recaptcha_site_secret": "Re-Captcha site secret", "obtain_credential": "Obtain your Google Re-Captcha credentials", "here": "here", "v3_setup_description": "Must use Re-Captcha v3 in credentials setup. Be careful, the wrong settings will make the login system interruption.", "announcement_setting": "Site-Wide Announcements", "announcement": "Announcement", "announcement_settings_description": "Create and manage global messages that can be displayed across the application to communicate important updates or information.", "is_Enable": "Is Enable", "link": "Link", "link_text": "Link Text", "message": "Message", "cronjob": "<PERSON><PERSON><PERSON><PERSON>", "cronjob_description": "Schedule and manage automated background tasks, ensuring timely execution of system maintenance and periodic processes.", "cronjob_running": "Congratulations! Your cronjob is running.", "last_checked_at": "Last checked at", "setting_up_the_cronjob": "Setting up the Cronjob", "cronjob_1": "Connect to your server via SSH or any preferred method.", "cronjob_2": "Open the crontab file using a text editor (e.g., `crontab -e`).", "cronjob_3": "Add the above command to the crontab file and save it.", "cronjob_4": "The cronjob will now run at every minute and execute the specified command.", "link_description": "You can learn more about cronjob from the Laravel", "seo": "SEO", "seo_description": "Configure meta tags, sitemap settings, and other SEO-related parameters to improve your application's search engine visibility.", "meta_title": "Meta Title", "meta_description": "Meta Description", "pusher": "<PERSON><PERSON><PERSON>", "app_id": "App ID", "app_key": "App Key", "app_secret": "App Secret", "cluster": "Cluster", "leave_blank_for_default_cluster": "Leave blank to use the default pusher cluster.", "enable_real_time_notifications": "Enable Real Time Notifications", "enable_desktop_notifications": "Enable Desktop Notifications", "ssl_required_for_desktop_notifications": "SSL is required for desktop notifications.", "test_pusher": "Test Pusher", "test_connection": "Test Connection", "dest_notify_desc": "Starting from September 2024-25 the application must run on SSL in order desktop notifications to work properly (browsers requires SSL).", "google_chrome": "Google Chrome.", "auto_dismiss_desktop": "Auto Dismiss Desktop Notifications After X Seconds (0 to disable)", "api_settings": "API Settings", "api_token": "API Token", "api_token_description": "Configure your API token settings below.", "enable_api_access": "Enable API Access", "generate_new_token": "Generate New Token", "copy": "Copy", "copied": "<PERSON>pied", "please_copy_your_new_api_token_now": "Please copy your new API token now.", "these_are_the_default_permissions_for_api_access": "These are the default permissions for API access", "success": "Success", "api_management": "API Management", "performance_optimization": "Performance Optimization", "token_abilities": "Token Abilities", "cache_description": "Configure caching strategies to improve application speed and reduce server load through intelligent data storage.", "clear_framework_text": "Clear framework cache, bootstrap cache and temporary files.", "view_text": "Clear compiled views to make views up to date.", "clear_config": "Clear config - You might need to refresh the config caching when you change something on production environment.", "clear_cache_routing": "Clear cache routing.", "clear_system_log_file": "Clear system log files.", "size": "Size:", "run_tool": "<PERSON>", "processing": " Processing...", "processing_cache_clearing": "Processing cache clearing...", "system_update": "System Update", "software_update_management": "Software Update Management", "software_update_management_description": "Manage application version updates, check for new releases, and control the update deployment process.", "system_information": "System Information", "please_select_an_option": "Please select an option", "packages_installed": "Packages Installed", "system_environment": "System Environment", "core_version": "Core Version:", "framework_version": "Framework Version:", "debug_mode": "Debug Mode:", "what_is_this": "What is this?", "storage_dir_writable": "Storage Dir Writable:", "cache_dir_writable": "<PERSON><PERSON>:", "app_size": "App Size:", "free_total_disk_space": "Free/Total Disk Space:", "php_max_execution_time": "PHP Max Execution Time(s):", "server_software": "Server Software:", "server_os": "Server OS:", "database": "Database:", "cache_driver": "<PERSON><PERSON> Driver:", "session_driver": "Session Driver:", "queue_connection": "Queue Connection:", "extension": "Extension:", "whatsapp_auto_lead": "Whatsapp Auto Lead", "stop_bot": "Stop Bot", "web_hooks": "Whatsapp Webhook", "support_agent": "Support Agent", "notification_sound": "Notification Sound", "ai_integration": "AI Integration", "auto_clear_chat_history": "Auto Clear Chat History", "whatsmark_settings": "Whatsmark Settings", "automate_lead_generation": "Automate lead generation and management through WhatsApp integration.", "acquire_new_lead_automatically": "Acquire New Lead Automatically (convert new WhatsApp messages to lead)", "lead_status": "Lead Status", "lead_source": "Lead Source", "lead_assigned": "Lead Assigned", "configure_stop_bot": "Configure settings to prevent unwanted bot activities in the system.", "stop_bots_keyword": "Stop Bots Keyword", "restart_bots_after": "<PERSON><PERSON>", "hours": "Hours", "manage_web_hooks": "Manage webhooks to enable seamless integration with external services.", "enable_webhooks_resend": "Enable WebHooks Re-send", "webhook_resend_method": "Webhook Resend Method", "get": "GET", "post": "POST", "whatsapp_received_data_resend_to": "WhatsApp received data will be resent to", "configure_support_agent": "Configure support agent settings for streamlined customer service.", "restrict_chat_access": "Restrict chat access to assigned support agents only.", "note": "Note:", "support_agent_feature_info": "When you enable the support agent feature, the staff will automatically be assigned to the chat. <PERSON><PERSON> can also assign a new agent from the chat page.", "customize_notification_sound": "Customize notification sounds for better user experience.", "enable_whatsapp_chat_notification_sound": "Enable WhatsApp chat notification sound", "integrate_ai_tools": "Integrate AI-powered tools to enhance automation and decision-making.", "activate_openai_in_chat": "Activate OpenAI in the chat.", "chat_model": "Chat Model", "openai_secret_key": "OpenAI Secret Key", "where_to_find_secret_key": "Where you can find secret key?", "clear_chat_history": "Clear Chat History", "setup_auto_clear_chat": "Set up automated clearing of chat histories to maintain system performance and privacy.", "activate_auto_clear_chat": "Activate Auto Clear Chat History", "auto_clear_history_time": "Auto Clear History Time", "days": "Days", "enabling": "<PERSON><PERSON><PERSON>", "auto_clear_note": " will automatically delete old chats after the specified number of days when the", "cron_job": "cron job", "runs": "runs.", "cron_job_required": "This feature requires a properly configured", "cron_job_setup_info": "Before activating, ensure it is set up as per the", "documentation": "documentation.", "template": "Template", "variables": "Variables", "variable": "Variable", "variable_description": "Currently, the variable is not available for this template.", "select_document": "Select Document", "select_image": "Select Image", "select_video": "Select Video", "body": "Body", "send_campaign": "Send Campaign", "create_message_bot": "Create Message Bot", "message_bot": "Message Bot", "nothing_selected": "Nothing Selected", "reply_button": "<PERSON><PERSON>", "cta_url": "CTA URL", "file_upload": "Files", "welcome_back": "Welcome Back,", "whatsapp_business_update": "Here's what's happening with your WhatsApp business today.", "new_campaign": "New Campaign", "view_all_reports": "View All Reports", "recent_activities": "Recent Activities", "view_all": "View All", "bot_performance": "Bot <PERSON>", "configure_bots": "Configure <PERSON>", "interactions": "interactions", "success_percentage": "% Success", "whatsapp_template": "Whatsapp Template", "load_template": "Load Templates", "template_management": "Template Management", "announcement_toggle_note": "(Enable this option to display an announcement on the login page.)", "new_user": "New User", "delete_user": "Delete User", "users": "Users", "user": "User", "add_user": "Add User", "role": "Role", "profile_image": "Profile Image", "remove": "Remove", "profile_image_removed_successfully": "Profile image removed successfully.", "change": "Change", "new_role": "New Role", "delete_role": "Delete Role", "permission": "Permission", "phone": "Phone", "select_default_language": "Select Default Language", "select_role": "Select Role", "save": "Save", "activity_log_list": "Activity Log List", "clear_log": "Clear Log", "activity_log_details": "Activity Log Details", "date": "DATE", "total_parameter": "Total Parameters", "number_id_of_the_whatsapp": "Number Id of the WhatsApp Registered Phone", "business_account_id": "WhatsApp Business Account ID", "whatsapp_access_token": "WhatsApp Access Token", "raw_content": "Raw Content", "format_type": "Format Type: JSON", "response": "Response", "something_went_wrong": "Something went wrong! Try again", "account_disconnected": "Account disconnected", "health_status_updated": "Health status updated", "default_number_updated": "Default number updated", "languages": "Languages", "language_name": "Language Name", "language_code": "Language Code (ISO Code)", "delete_language": "Delete Language", "translation_management": "Translation Management", "translate_language": "Translate Language", "activity_log": "Activity Log", "templates": "Templates", "marketing": "Marketing", "template_bot": "Temp<PERSON>", "support": "Support", "chat": "Cha<PERSON>", "setup": "Setup", "email_verification": "Thanks for signing up! Please verify your email by clicking the link we sent you. If you didn't receive the email, we will gladly send another.", "verify_email": "<PERSON><PERSON><PERSON>", "email_veri": "Email Verification", "phone_sample": "****** 123 4567", "sample_data": "Sample Data", "import_contact": "Import Contacts", "import_contact_camel": "Import Contact", "import_contact_from_csv_file": "Import contacts from CSV file", "drag_and_drop_description": "Drag your file here or click in this area.", "pusher_link": "https://pusher.com/docs/clusters", "send_test_email": "Send Test Email", "send_test_email_description": "Send test email to make sure that your SMTP settings is set correctly.", "template_bot_saved_successfully": "Template bot saved successfully", "create_template_bot": "Create Temp<PERSON> <PERSON>", "edit_template_bot": "Edit Template <PERSON>", "create_campaign": "Create Campaign", "select_all_relation_type": "Select All Relation type", "select_all_leads": "Select all", "or": "OR", "schedule_send_time": "Scheduled Send Time", "ignore_scheduled_time_and_send_now": "Ignore scheduled time and send now", "variable_not_available_for_this_template": "Currently, the variable is not available for this template.", "document_uploaded": "Document uploaded:", "this_campaign_send_to": "This campaign sent to :", "notification": "Notification", "clear_all": "Clear All", "development_warning_title": "Application is running in development/debug mode!", "development_warning_content": "To optimize performance and security, change the settings in your `.env` file as follows:", "app_env": "APP_ENV:", "app_debug": "APP_DEBUG:", "production": "production", "debug_false": "false", "development_warning_details": "In development or debug mode, you may encounter detailed errors and deprecation warnings.", "performance_security_tip": "Always use production mode unless actively working on new features or debugging.", "change_language": "Change Language", "edit": "Edit", "ids": "ID", "clone": "<PERSON><PERSON>", "type_and_press_enter": "Type and press Enter..", "delete_campaign_title": "Delete Campaign", "edit_campaign": "Edit Campaign", "campaign_created_successfully": "Campaign saved successfully", "campaign_update_successfully": "Campaign updated successfully", "run_cron_manually": "<PERSON>ly", "running": "Running", "cron_not_running": "<PERSON><PERSON> Running", "cron_last_run": "Last Run", "please_check_cron_setup": "Please check your cron job setup. It has not run in the last 48 hours.", "delete_account": "Delete Account", "delete_account_message": "Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain.", "account_delete_confirmation": "Are you sure you want to delete your account?", "account_delete_password": "Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account.", "update_password": "Update Password", "update_password_message": "Ensure your account is using a long, random password to stay secure.", "current_password": "Current Password", "new_password": "New Password", "saved": "Saved", "profile_information": "Profile Information", "update_profile_information": "Update your account's profile information and email address.", "email_unverified": "Your email address is unverified.", "resend_email_verification": "Click here to re-send the verification email.", "verification_link_sent_to_email": "A new verification link has been sent to your email address.", "csv_file_only": "CSV file only", "csv_uploaded_successfully": "CSV uploaded successfully", "please_upload_valid_csv_file": "Please upload valid CSV File", "import_failed": "Import failed", "please_select_csv_file": "Please select CSV File", "file_selected": "File selected", "import_completed": "Contacts imported successfully", "leads": "leads", "campaign_details": "Campaign Details", "back_to_campaigns": "Back to Campaigns", "create_new_campaign": "Create New Campaign", "campaign_name_capital": "CAMPAIGN NAME", "status_capital": "STATUS", "executed": "EXECUTED", "template_capital": "TEMPLATE", "scheduled_at_capital": "SCHEDULED AT", "total_contacts": "TOTAL CONTACTS", "all_contacts_from_group": "All contacts from group", "total": "Total ", "total_delivered": "TOTAL DELIVERED", "messages": "Messages", "total_read": "TOTAL READ", "total_read_messages": "Messages Read", "total_failed": "TOTAL FAILED", "rate": "Rate", "out_of_the": "Out of the", "records_in_your_csv_file": "records in your CSV file,", "records_are_valid": "records are valid.", "campaign_successfully_sent_to_these": " The campaign can be successfully sent to these", "from": "From:", "all_chats": "All Chats", "searching": "Searching", "filters": "Filters", "select_type": "Select Type", "agents": "Agents", "groups": "groups", "select_group": "Select Group", "select_source": "Select Source", "select_status": "Select Status", "select_assign": "Select Assigned Name", "click_user_to_chat": "Click user to chat", "24_hours_limit": "24 hours limit", "whatsapp_block_message_24_hours_after": "WhatsApp blocks messages 24 hours after", "the_last_template_message_still_be_sent": "the last contact, but template messages can still be sent.", "search": "Search", "reply_within": "Reply within", "hours_and": "hours and", "minutes_remaining": "minutes remaining", "share": "Share", "user_information": "User Information", "download_document": "Download Document", "delete_notes_title": "Delete Note", "reply": "Reply", "details": "Details", "replying_to": " Replying to:", "your_broser_not_support_video_tag": " Your browser does not support the video tag.", "user_info": "User Info", "select_language": "Select Language", "english_to": "English To", "active": "Active", "support_has_already_expired": "Support has already expired.", "renew_support": "Renew Support", "do_you_want_custom_service": "Do you want custom services? Visit here and create your ticket", "create_support_ticket": "Create Support Ticket", "version_information": "Version Information", "your_version": "Your Version", "latest_version": "Latest Version", "purchase_key": " Purchase Key", "username": "Username", "download_update": "Download Update", "before_update_description": "Before performing an update, it is strongly recommended to create a full backup of your current installation (files and database) and review the changelog.", "change_log": "Change Log", "version": "Version", "added_new_feature_and_improvements": "Added new features and improvements", "bug_fixes_and_performance_improvements": "Bug fixes and performance improvements", "campaign_paused_successfully": "Campaign paused successfully", "campaign_resumed_successfully": "Campaign resumed successfully", "resume_campaign": "Resume Campaign", "pause_campaign": "Pause Campaign", "dashboard_overview": "Dashboard Overview", "note_will_be_available_in_contact": "Notes will be available once the contact is created.", "other_information": "Other Information", "no_notes_available": "No notes available.", "records_processed": "records processed", "record_successfully_inserted": " Records successfully inserted:", "records_with_error": "Records with errors:", "import_error": "Import Errors", "prompt_action": "Prompt Action", "sent_status": "Sent status", "delivered_to": "Delivered To", "ready_by": "Read By", "created_at": "Created At", "not_assigned": "Not Assigned", "public": "Public", "canned_reply_activate": "Canned reply is activated", "canned_reply_deactivate": "Canned reply is deactivated", "no_contact_selected": "No contacts selected", "contact_enable_successfully": "Contact enabled successfully", "contact_disabled_successfully": "Contact disabled successfully", "code": "Code", "translate": "Translate", "canned_replies": "Canned Replies", "creation_time": "Creation Time", "last_activity": "Last Activity", "translation_updated_successfully": "Translation updated successfully", "message_bot_is_activated": "Message Bo<PERSON> is activated", "message_bot_is_deactivated": "Message Bot is deactivated", "message_bot_not_found": "Message bot not found", "invalid_file_format": "Invalid file name format", "original_file_not_found": "Original file not found", "bot_clone_successfully": "Bot clone successfully", "template_bot_activate": "Template <PERSON><PERSON> is activate", "template_bot_deactivate": "Template Bot is deactivate", "template_bot_not_found": "Template bot not found", "category": "Category", "template_type": "Template Type", "body_data": "Body Data", "delete_templatebot": "Delete Template <PERSON>", "response_code": "Response Code", "view": "View", "bulk_delete": "Bulk Delete", "sql_injection_error": "This field contain invalid characters, script, or HTML.", "message_bot_saved_successfully": "Message bot saved successfully", "delete_message_bot_successfully": "Message bot deleted successfully", "template_bot_delete_successfully": "Template bot deleted successfully", "campaign_delete_successfully": "Campaign deleted successfully", "file_not_found": "File not found", "download_successful": "Download successful", "ai_prompt_saved_successfully": "AI Prompt saved successfully", "ai_prompt_updated_successfully": "AI Prompt updated successfully", "ai_prompt_delete_successfully": "AI Prompt deleted successfully", "canned_reply_save_successfully": "Canned Reply saved successfully", "canned_reply_update_successfully": "Canned Reply updated successfully", "canned_reply_delete_successfully": "Canned Reply deleted successfully", "note_added_successfully": "Note added successfully", "note_delete_successfully": "Note deleted successfully", "contact_created_successfully": "Contact created successfully", "contact_update_successfully": "Contact updated successfully", "contacts_delete_successfully": "Contacts deleted successfully", "contact_delete_success": "Contact deleted successfully", "an_error_occured_deleting_contact": "An error occurred while deleting the contacts", "phone_validation": "The phone number must be in international format (e.g., +12345678901).", "invalid_csv_file": "Invalid CSV file", "missing_required_columns": "Missing required column", "sample_file_not_found": "Sample file not found", "source_saved_successfully": "Source saved successfully", "source_update_successfully": "Source updated successfully", "source_delete_successfully": "Source deleted successfully", "status_save_successfully": "Status saved successfully", "status_update_successfully": "Status updated successfully", "status_delete_successfully": "Status deleted successfully", "status_delete_in_use_notify": "The Status ID is already being used", "no_activity_log_found": "No activity logs found to delete", "activity_logs_deleted": " Activity logs deleted", "system_default": "System Default", "log_deleted": "Log deleted", "log_not_found": "Log not found", "clear_all_logs": "Clear all logs", "template_activate_successfully": "Template activated successfully", "template_deactivate_successfully": "Template deactivated successfully", "email_template_update_successfully": "Email-Template updated successfully!", "role_save_successfully": "Role saved successfully", "role_delete_successfully": "Role deleted successfully", "profile_update_successfully": "Profile updated successfully", "edit_english_language_not_allowed": "Editing the English language is not allowed", "language_update_successfully": "Language updated successfully", "language_added_successfully": "Language added successfully", "language_handling_error": "There was an error while handling the language files", "deleting_the": "Deleting the", "language_is_not_allowed": "Language is not allowed", "language_delete_successfully": " language deleted successfully", "setting_save_successfully": "Setting<PERSON> saved successfully", "cache_cleared_successfully": " cache cleared successfully", "cache_management": "Cache Management", "never": "Never", "failed_to_execute_cron_job": "Failed to execute cron job:", "email_config_is_required": "Email Configuration is required", "email_sent_successfully": "<PERSON>ail sent successfully", "failed_to_send_test_mail": "Failed to send test email:", "remove_successfully": " removed successfully!", "api_setting_update_successfully": "API settings updated successfully", "fill_required_pusher_credential": "Please fill all required P<PERSON><PERSON> credentials first", "pusher_is_not_initialized": "Pusher service is not initialized, Please check your credentials", "hello": "Hello", "your_real_time_notification": "your real-time notification setup is good to go!", "pusher_connection_test_successful": "Pusher connection test successful!", "pusher_connection_test_failed": "Pusher connection test failed, Check logs for more details", "pusher_test_connection_error": "Pusher test connection error:", "pusher_test_connection_failed": "Pusher test connection failed:", "user_save_successfully": "User saved successfully", "user_update_successfully": "User updated successfully", "user_delete_successfully": "User deleted successfully", "user_in_use_notify": "The User ID is already being used", "webhook_connect_successfully": "Webhook connected successfully", "account_connect_successfully": "Account connected successfully", "recipient_phone_number_required": "Recipient phone number is required", "message_template_required": "Message template is required", "invalid_phone_number_format": "Invalid phone number format", "language_file_for": "Language file for", "not_found": "Not found", "failed_to_decode_json_from": "Failed to decode JSON from ", "error_loading_language_file": "Error loading language file:", "error_fetching_language_key_value": "Error fetching language key value: ", "today": "Today", "yesterday": "Yesterday", "notification_marked_as_read": "Notification marked as read", "all_notification_marked_as_read": "All notifications marked as read", "notification_delete_successfully": "Notification deleted successfully", "all_notification_cleared": "All notifications cleared", "just_now": "just now", "min_ago": "min ago", "hours_ago": "hours ago", "days_ago": "days ago", "failed_to_fetch_contact": "Failed to fetch contacts", "user_not_found": "User not found", "thank_you_for_signing_up": "Thank you for signing up! Please verify your email address by clicking the link below:", "email_sent_successfull_with_emoji": "Email sent successfully! 🎉", "email_not_sent_try_again": "Oops! Email Not Sent Please try again or contact support.", "email_service_not_configured": "Email service is not configured properly. Please contact the administrator.", "email_recaptcha_failed": "reCAPTCHA verification failed. Please try again.", "provided_credential_not_match": "The provided credentials do not match our records.", "verification_link_sent": "verification-link-sent", "verification_error": "verification-error", "password_updated": "password-updated", "failed_to_send_password_reset_link": "Failed to send password-reset email. Please contact the administrator.", "profile_update": "profile-updated", "access_denied": "Access denied", "api_access_is_disabled": "API access is disabled", "invalid_api_token": "Invalid API token", "token_not_have_required_abilities": "<PERSON><PERSON> does not have the required abilities", "failed_to_load_template": "Failed to load templates", "template_not_found": "Temp<PERSON> not found", "template_id": "Template ID", "no_found_in_whatsapp_business_account": "not found in WhatsApp Business Account", "is_not_approved": "is not approved. Current status:", "modules": "<PERSON><PERSON><PERSON>", "prepare_to_send_campaign_message": "Preparing to send campaign message", "campaign_message_result": "Campaign message result", "campaign_message_failed": "Campaign message failed", "whatsapp_message_sent_successfully": "WhatsApp message sent successfully", "whatsapp_message_failed": "WhatsApp message failed", "failed_to_send_whatsapp_message": "Failed to send WhatsApp message after", "attempts": "attempts", "whatsapp_message_failed_permanently": "WhatsApp message failed permanently", "other_group": "other-group", "default_subject": "Default Subject", "default_body_content": "Default Body Content", "there": "there", "verify_email_notification_error": "VerifyEmail Notification Error:", "build_mail_message_error": "Build Mail Message Error: ", "verification_url_generation_error": "Verification URL Generation Error: ", "set_verification_url_callback_error": "Set Verification URL Callback Error: ", "set_mail_callback_error": "Set Mail Callback Error: ", "lear_created_event_occurred": "Lead Created Event occurred:", "whatsapp_message_job_failed": "WhatsApp message job failed", "attack_injection_risk": ":attribute contains invalid input (HTML, SQL, or JSON injection risk).", "potential_sql_injection": "Potential SQL injection detected.", "potential_json_injection": "Potential JSON injection detected.", "validation_failed_due_to_unexpected_error": "Validation failed due to an unexpected error.", "failed_to_send_email": "Failed to send email: ", "class": "Class", "does_not_exist": "does not exist.", "must_implement": " must implement 'name' and 'build' methods.", "module_json_file_not_found": "module.json file not found", "invalid_json_format": "Invalid JSON format in module.json", "invalid_version_format": "Invalid version format. Must follow semantic versioning (e.g., 1.0.0)", "module_must_have_service_provider": "Module must have at least one service provider", "invalid_provider_format": "Invalid provider format", "pusher_inti_failed": "Pusher initialization failed: ", "pusher_initialization_failed": "<PERSON>usher is not initialized", "pusher_batch_trigger_failed": "Pusher batch trigger failed", "pusher_not_initialized": "<PERSON><PERSON><PERSON> not initialized", "pusher_trigger_failed": "Pusher trigger failed: ", "unable_to_read_composer_lock": "Unable to read composer.lock", "module_manager": "Module Manager", "upload_module": "Upload Module", "upload_now": "Upload Now", "uploading": "Uploading...", "module": "<PERSON><PERSON><PERSON>", "documentations": " Documentation", "core": "Core", "addon": "<PERSON><PERSON>", "inactive": "Inactive", "install": "Install", "reset": "Reset", "select_model": "Select Model", "settings_webhook": "Webhook Management", "select_smtp_encryption": "Select SMTP Encryption", "bot_protection": "Bot Protection", "scheduled_tasks_management": "Scheduled Tasks Management", "search_engine_optimization": "Search Engine Optimization", "real_time_event_broadcasting": "Real-Time Event Broadcasting", "real_time_event_broadcasting_description": "Set up real-time communication channels for instant updates and live interactions across your application.", "api_integration_and_access": "API Integration & Access", "api_integration_and_access_description": "Securely extend your application's functionality through our robust API, enabling seamless data exchange, custom integrations, and programmatic access to your system's core resources", "contacts": "Contacts", "contacts_create": "Contacts Create", "contacts_read": "Contacts Read", "contacts_update": "Contacts Update", "contacts_delete": "Contacts Delete", "statuses": "Statuses", "status_create": "Status Create", "status_read": "Status Read", "status_update": "Status Update", "status_delete": "Status Delete", "sources": "Sources", "source_create": "Source Create", "source_read": "Source Read", "source_update": "Source Update", "source_delete": "Source Delete", "webhook_integrations": "Webhook Integrations", "webhook_integrations_description": "Webhooks allow real-time notifications and seamless data synchronization between your application and external services. Configure endpoints to receive instant updates about contacts, statuses, and other critical events in your system.", "enable_webhook_access": "Enable Webhook Access", "webhook_abilities": "Webhook Abilities", "default_permissions_for_webhook_access": "Default permission for webhook access", "general": "General", "default_phone_number": "Default Phone Number", "messaging_limit": "Message Limit", "additional_phone_number": "Additional Phone Number", "messages_sent_today": "messages sent today", "increase_webhook_note": " Enabling this option will increase your webhook load. For more info visit this", "fix_spelling_and_grammar": "Fix Spelling & Grammar", "simplify_language": "Simplify Language", "get_qr_code": "Get QR Code", "disconnect": "Disconnect", "disconnect_message": "Are You Sure You Want to Disconnect?", "add": "Add", "lead": "Lead", "customer": "Customer", "users_using_this_role": "List of users using this role", "total_send_campaign_list": "Total Send Campaign List", "please_add_valid_number_in_csv_file": "Please add valid number in csv file", "chat_cleanup_completed": "Chat Cleanup Summary", "found_messages": "Found :count messages older than the specified timeframe.", "deleted_messages": "Deleted :count old messages.", "deleted_conversations": "Removed :count empty chat conversations.", "error_during_cleanup": "An error occurred during the cleanup process.", "dismiss": "<PERSON><PERSON><PERSON>", "run_cleanup_now": "Run Cleanup Now", "confirm_run_cleanup": "Are you sure you want to run the cleanup now? This will delete old messages based on your settings.", "chat_cleanup_completed_successfully": "Chat cleanup completed successfully.", "chat_cleanup_failed": "Chat cleanup failed", "please_specify_days_to_keep": "Please specify the number of days to keep messages before running cleanup.", "edit_role": "Edit Role", "send_welcome_mail": "Send Welcome Email", "send_verification_mail": "Is Verified User", "email_template_editor": "Email Template Editor", "template_name": "Template Name", "subject": "Subject", "user_fields": "User Fields", "contact_fields": "Contact Fields", "other_fields": "Other Fields", "save_template": "Save Template", "email_template_updated_successfully": "Email Template updated successfully", "available_merge_fields": "Available Merge Fields", "user_not_verified": "Access restricted. Please verify your account to proceed.", "no_merge_fields_available": "No available merge fields for this template type", "of_total_leads": "of your total leads", "total_fail": "Messages Failed", "messages_delivered": "Messages Delivered", "sending_campaign": "Sending Campaign...", "this_may_take_a_few_moments": "This May Take A Few Moments", "sending_to": "Sending To", "this_trigger_already_exists": "This trigger keyword already exists.", "cover_page_image": "Cover Page Image", "personal_information": "Personal Information", "roles_and_permissions": "Roles & Permissions", "permissions": "Permissions", "administrator_access": "Administrator Access", "enter_first_name": "Enter first name", "enter_last_name": "Enter last name", "enter_email_address": "Enter email address", "enter_password": "Enter password", "leave_blank_to_keep_current_password": "Leave blank to keep current password", "toggle_admin": "Toggle Administrator", "on": "ON", "off": "OFF", "sends_welcome_email_to_new_user": "Sends a welcome email to the user when their account is created", "mark_email_as_verified_or_send_verification": "Mark email as verified or send verification email", "required_fields": "Required fields", "from_role": "from role", "administrator_info": "Administrator Information", "administrators_have_full_access_to_all_features_and_settings": "Administrators have full access to all features and settings of the system.", "admin_user_has_full_access_to_all_features": "Administrator users have unrestricted access to all features and functions.", "tap_on_a_feature_to_view_permissions": "Tap on a feature to view available permissions", "tip": "Tip", "use_browser_search": "Use browser search", "to_find_specific_permissions": "to find specific permissions", "user_create_success": "User created successfully", "user_update_success": "User updated successfully", "user_delete_success": "User deleted successfully", "user_delete_confirm": "Are you sure you want to delete this user?", "password_mismatch": "Passwords do not match", "password_changed_success": "Password changed successfully", "password_changed_error": "Error changing password", "profile_image_upload_error": "Error uploading profile image", "profile_image_remove_success": "Profile image removed successfully", "validation_required": "The :attribute field is required.", "validation_email": "The :attribute must be a valid email address.", "validation_unique": "The :attribute has already been taken.", "validation_min": "The :attribute must be at least :min characters.", "validation_confirmed": "The :attribute confirmation does not match.", "not_allowed_to_view": "Not allowed to view.", "role_in_use_notify": "The Role is already being used", "enter_role_name": "Enter role name", "your_campaign_is_already_executed": "Your Campaign is already executed", "failed": "Failed", "in_progress": "In Progress", "remove_chat": "Remove <PERSON>", "info": "Information", "emojis": "Emojis", "attach_img_doc_vid": "Attach Image Video Documents", "record_audio": "Record Audio", "more": "More", "click_to_open_leads": "Click to open leads", "this_field_is_required": "This field is required", "message_statistics": "Message statistics", "system_status": "System status", "whatsapp_connection": "Whatsapp connection", "api_status": "Api status", "queue_size": "Queue size", "daily_api_calls": "Daily api calls", "this_week": "This week", "last_week": "Last week", "month": "Month", "system_logs": "System Logs", "support_agent_assigned_successfully": "Support agent assigned successfully", "connect_account": "Connect Account", "log_viewer": "Log Viewer", "no_log_files": "No log files", "refresh": "Refresh", "per_page": "per page", "emergency": "EMERGENCY", "alert": "ALERT", "critical": "CRITICAL", "error": "ERROR", "warning": "WARNING", "notice": "NOTICE", "info_log": "INFO", "debug": "DEBUG", "local": "LOCAL", "level": "Level", "content": "Content", "actions": "Actions", "no_log_file_selected": "No log file selected", "no_log_entries_found_the_file_may_be_empty": "No log entries found. The file may be empty.", "no_log_entries": "No log entries matching your criteria.", "showing_page": "Showing page", "of": "of", "last": "Last", "next": "Next", "previous": "Previous", "environment": "Environment", "delete_log_file": "Delete Log File", "delete_log_file_confirmation": "Are you sure you want to delete this log file? This action cannot be undone.", "contact_information": "Contact Information", "webhook_discoonected_successfully": "Webhook disconnected successfully", "error_processing_ai_response": "Error processing AI response", "chat_delete_successfully": "<PERSON><PERSON> deleted successfully", "access_denied_note": "You're not authorized to access this feature", "user_deactivated_successfully": "User deactivated successfully", "user_activated_successfully": "User activated successfully", "user_deactivated_message_in_login": "Your account has been deactivated. Please contact support.", "account_cannot_be_deactivated": "This account cannot be deactivated.", "admin": "Admin", "pusher_account_setup": "<PERSON><PERSON><PERSON> Account <PERSON>up", "pusher_account_setup_description": " It seems that your <PERSON>usher account is not configured correctly. Please complete the setup to enable real-time features.", "access_system_settings": " Access System Settings", "navigate_to_whatsmark_system": " Navigate to Whatsmark System Settings → <PERSON><PERSON><PERSON>", "follow_documentation": "Follow Documentation", "read_the_whatsmark_documentation": "Read the Whatsmark documentation for detailed setup instructions", "real_time_notification_require_pusher_integration": "Real-time notifications require <PERSON><PERSON><PERSON> integration", "change_tone": "Change Tone", "no_country_found": " No country found.", "custom_prompt": "Custom Prompt", "no_result_found": "No results found.", "recaptcha_verification_failed": "Recaptcha verification failed", "failed_to_fetch_sources": "Failed to fetch sources", "failed_to_fetch_statuses": "Failed to fetch statuses", "message_bot_save_failed": "Message bot save failed", "campaign_save_failed": "Campaign save failed", "file_upload_failed": "File upload failed", "ai_prompt_save_failed": "AI prompt save failed", "ai_prompt_edit_failed": "AI prompt edit failed", "ai_prompt_delete_failed": "AI prompt delete failed", "canned_reply_save_failed": "Canned reply save failed", "canned_reply_edit_failed": "Canned reply edit failed", "canned_reply_delete_failed": "Canned reply delete failed", "error_deleting_notes": "Error deleting notes", "mail_sending_failed": "Mail sending failed", "mail_successfully_sent": "Mail Successfully Sent.", "invalid_email_address": "Invalid email address", "redis_connection_failed": "Redis connection failed:", "database_info_retrieval_failed": "Database info retrieval failed:", "unable_to_retrieve_database_information": "Unable to retrieve database information", "error_executing_shell_command": "Error executing shell command", "failed_to_clear_cache_after_env_change": "Failed to clear cache after env change:", "environment_file_changed_cache_cleared": "Environment file changed, cache cleared ", "failed_to_delete_backup": "Failed to delete backup ", "backup_failed": "Backup failed ", "failed_to_remove_profile_image": "Failed to remove profile image.", "campaign_error": "Campaign error", "message_deleted_successfully": "Message deleted successfully", "source_save_failed": "Source save failed", "source_delete_failed": "Source delete failed", "status_save_failed": "Status save failed", "status_delete_failed": "Status delete failed", "email_template_update_failed": "Email template update failed", "role_save_failed": "Role save failed", "profile_update_failed": "Profile update failed", "language_delete_failed": "Language delete failed", "the_translation_cannot_be_a_JSON_object_or_array": "The translation cannot be a JSON object or array.", "user_save_failed": "User save failed", "account_connect_failed": "Account connect failed", "webhook_disconnect_failed": "Webhook disconnect failed", "webhook_connect_failed": "Webhook connect failed", "dynamic_input_error": "Some fields contain invalid characters or unsupported content.", "your_account_is_discconected": "Your Account Is Disconnected!", "disconnected_info": "Your account is no longer connected to our system. This may be due to an expired token, a disconnected webhook, an invalid token, or changes in your Meta account settings.", "allowed_fromats_jpeg_png_max_5": "Allowed formats: jpeg, png (Max: 5MB)", "new_password_changed": "Password has been changed successfully.", "change_password_heading": "Change Your Password", "no_permission_to_perform_action": "You don't have permission to perform this action", "in_this_campaign": "In This Campaign", "enable_debug_mode": "Enable Debug Mode", "enable_whatsapp_log": "Enable Whatsapp Log", "environment_updated": "Environment updated!", "whatsapp_log_updated": "Whatsapp log updated!", "enable_production_mode": "Enable production mode", "enable_production_mode_successfully": "Enable production mode successfully", "disable_production_mode_successfully": "Disable production mode successfully", "changelog": "Changelog", "new_feature": "New Feature", "improvement": "Improvement", "bug_fix": "Bug Fix", "cron_job_executed_successfully": "<PERSON><PERSON> executed successfully", "resend_campaign": "Resend Campaign", "campaign_resend_process_initiated": "Campaign resend process initiated", "you_cant_resend_this_campaign": "you can not resend this campaign"}