<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['disabled' => false, 'size']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['disabled' => false, 'size']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
  $size = [
      'xs' => 'px-2.5 py-1.5 text-xs',
      'sm' => 'px-3 py-2 text-sm leading-4',
      'md' => 'px-4 py-2 text-sm',
      'lg' => 'px-4 py-2 text-base',
      'xl' => 'px-6 py-3 text-base',
  ][$size ?? 'md'];
?>

<button <?php echo e($disabled ? 'disabled' : ''); ?>

  <?php echo e($attributes->merge(['class' => "inline-flex items-center justify-center $size border border-transparent rounded-md font-medium disabled:opacity-50 disabled:pointer-events-none transition"])); ?>>
  <?php echo e($slot); ?>

</button>
<?php /**PATH C:\laragon\www\mywhats\resources\views/components/button.blade.php ENDPATH**/ ?>