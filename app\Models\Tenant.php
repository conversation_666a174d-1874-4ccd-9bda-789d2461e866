<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Tenant extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'domain',
        'status',
        'subscription_plan_id',
        'subscription_status',
        'trial_ends_at',
        'settings'
    ];

    protected $casts = [
        'settings' => 'array',
        'trial_ends_at' => 'datetime'
    ];

    /**
     * Get the subscription plan for this tenant
     */
    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class);
    }

    /**
     * Get all users associated with this tenant
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'tenant_users')
                    ->withPivot(['role', 'permissions'])
                    ->withTimestamps();
    }

    /**
     * Get tenant usage records
     */
    public function usage(): HasMany
    {
        return $this->hasMany(TenantUsage::class);
    }

    /**
     * Check if tenant has a valid subscription
     */
    public function hasValidSubscription(): bool
    {
        if ($this->subscription_status === 'trialing' && $this->trial_ends_at && $this->trial_ends_at->isFuture()) {
            return true;
        }

        return $this->subscription_status === 'active';
    }

    /**
     * Check if tenant is within trial period
     */
    public function isInTrial(): bool
    {
        return $this->subscription_status === 'trialing' && 
               $this->trial_ends_at && 
               $this->trial_ends_at->isFuture();
    }

    /**
     * Get current usage for a specific resource
     */
    public function getCurrentUsage(string $resource): int
    {
        $currentPeriod = now()->format('Y-m');
        
        return $this->usage()
                   ->where('resource', $resource)
                   ->where('period', $currentPeriod)
                   ->value('count') ?? 0;
    }

    /**
     * Check if tenant is within usage limits for a resource
     */
    public function withinUsageLimit(string $resource): bool
    {
        if (!$this->subscriptionPlan) {
            return false;
        }

        $limit = $this->subscriptionPlan->limits[$resource] ?? 0;
        $currentUsage = $this->getCurrentUsage($resource);

        return $currentUsage < $limit;
    }

    /**
     * Check if tenant has access to a specific feature
     */
    public function hasFeature(string $feature): bool
    {
        if (!$this->subscriptionPlan) {
            return false;
        }

        return isset($this->subscriptionPlan->features[$feature]) && 
               $this->subscriptionPlan->features[$feature] > 0;
    }

    /**
     * Get the owner of this tenant
     */
    public function owner()
    {
        return $this->users()->wherePivot('role', 'owner')->first();
    }

    /**
     * Scope to get active tenants
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get tenants with valid subscriptions
     */
    public function scopeWithValidSubscription($query)
    {
        return $query->where(function ($q) {
            $q->where('subscription_status', 'active')
              ->orWhere(function ($subQ) {
                  $subQ->where('subscription_status', 'trialing')
                       ->where('trial_ends_at', '>', now());
              });
        });
    }
}

