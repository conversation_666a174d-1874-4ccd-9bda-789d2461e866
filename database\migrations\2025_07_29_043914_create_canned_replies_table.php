<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('canned_replies', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('shortcut')->nullable(); // Quick shortcut code
            $table->text('message');
            $table->string('category')->nullable(); // Category for organization
            $table->boolean('is_active')->default(true);
            $table->integer('usage_count')->default(0);
            $table->json('tags')->nullable(); // Tags for easier searching
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();
            
            $table->index(['category']);
            $table->index(['is_active']);
            $table->index(['shortcut']);
            $table->index(['created_by']);
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('canned_replies');
    }
};
