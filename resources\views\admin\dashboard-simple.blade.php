<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Test</title>
    @vite('resources/css/app.css')
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-8">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Admin Dashboard - Working!</h1>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-blue-500 text-white p-6 rounded-lg">
                    <h3 class="text-lg font-semibold">Total Messages</h3>
                    <p class="text-3xl font-bold">{{ \App\Models\ChatMessage::count() }}</p>
                </div>
                <div class="bg-green-500 text-white p-6 rounded-lg">
                    <h3 class="text-lg font-semibold">Total Contacts</h3>
                    <p class="text-3xl font-bold">{{ \App\Models\Contact::count() }}</p>
                </div>
                <div class="bg-purple-500 text-white p-6 rounded-lg">
                    <h3 class="text-lg font-semibold">Total Campaigns</h3>
                    <p class="text-3xl font-bold">{{ \App\Models\Campaign::count() }}</p>
                </div>
            </div>

            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-lg font-semibold mb-2">Debug Information</h3>
                <ul class="space-y-1 text-sm">
                    <li><strong>User:</strong> {{ Auth::user() ? Auth::user()->email : 'Not authenticated' }}</li>
                    <li><strong>Tenant:</strong> {{ app()->has('tenant') ? app('tenant')->name : 'No tenant resolved' }}</li>
                    <li><strong>Environment:</strong> {{ app()->environment() }}</li>
                    <li><strong>Route Name:</strong> {{ Route::currentRouteName() }}</li>
                </ul>
            </div>

            <div class="mt-6">
                <a href="{{ route('logout') }}" 
                   onclick="event.preventDefault(); document.getElementById('logout-form').submit();"
                   class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                    Logout
                </a>
                <form id="logout-form" action="{{ route('logout') }}" method="POST" class="hidden">
                    @csrf
                </form>
            </div>
        </div>
    </div>
</body>
</html>