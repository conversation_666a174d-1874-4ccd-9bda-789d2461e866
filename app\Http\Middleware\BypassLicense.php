<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class BypassLicense
{
    public function handle(Request $request, Closure $next)
    {
        // If accessing license route and license verification is disabled
        if ($request->is('install/license') && !config('installer.license_verification.required', true)) {
            // Set dummy license data in session
            session(['license_data' => [
                'username' => 'free-user',
                'purchase_code' => 'FREE-LICENSE-BYPASS',
                'verified' => true,
                'token' => 'FREE-TOKEN',
                'verification_id' => 'free-installation',
                'support_until' => now()->addYears(10)->format('Y-m-d')
            ]]);
            
            return redirect()->route('install.user');
        }
        
        return $next($request);
    }
}