{"preset": "laravel", "rules": {"array_syntax": {"syntax": "short"}, "binary_operator_spaces": {"default": "align_single_space_minimal"}, "cast_spaces": {"space": "single"}, "class_attributes_separation": {"elements": {"method": "one", "property": "one", "const": "one"}}, "concat_space": {"spacing": "one"}, "declare_equal_normalize": {"space": "single"}, "function_typehint_space": true, "lowercase_keywords": true, "method_argument_space": {"on_multiline": "ensure_fully_multiline", "keep_multiple_spaces_after_comma": false}, "no_extra_blank_lines": {"tokens": ["extra", "throw", "use"]}, "no_unused_imports": true, "not_operator_with_successor_space": true, "ordered_imports": {"imports_order": ["class", "function", "const"], "sort_algorithm": "alpha"}, "phpdoc_align": {"align": "vertical"}, "phpdoc_no_empty_return": false, "phpdoc_order": true, "phpdoc_summary": false, "return_type_declaration": {"space_before": "none"}, "single_quote": true, "ternary_operator_spaces": true, "trailing_comma_in_multiline": {"elements": ["arrays"]}, "visibility_required": {"elements": ["method", "property"]}, "yoda_style": false}, "paths": ["app", "config", "database", "routes", "tests"]}