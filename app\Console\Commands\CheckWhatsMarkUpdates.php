<?php

namespace App\Console\Commands;

// use Corbital\Installer\Classes\UpdateChecker; // Disabled for SaaS platform
use Illuminate\Console\Command;

class CheckWhatsMarkUpdates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'whatsmark:check-updates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for WhatsMarks updates and store the latest version in settings';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Disabled for SaaS platform - no longer checking for updates
        $this->info('Update checking is disabled for SaaS platform');
        
        // Set a default version
        set_setting('whats-mark.whatsmark_latest_version', config('app.version', '2.0.0'));
        
        return 0;
    }
}
